Feature:Camel PWA - Store Details
    As a RJR user
    I should be able access and validate the Camel store list and Mapview
    So that I can ensure the Store Details functionality works correctly

    @CamelStoreListView
    Scenario Outline: Validate Camel coupon StoreList and LoadMore Button
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I click on loadmore button and validate the list
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link

@CamelStoreListView_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude | Longitude |appName                 |
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.099861  | -80.2446  |Camel|

@CamelStoreListView_Prod
Examples:
| Brand   | URL                                      | Username                                     | Password  | Latitude | Longitude |appName                 |
| CAMEL | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.099861  | -80.2446  |Camel|


    @CamelStoreListSearchByZip 
    Scenario Outline: Validate Camel StoreList SearchBy valid and invalid Zip
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I validate Search By zip with invalid zip code
        Then I validate the error message for invalid zipcode
        When I validate Search By zip with valid zip code
        Then I select a store from store list page
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@CamelStoreListSearchByZip_QAstage
Examples:
| Brand   | URL                          | Username                                     | Password  | Latitude | Longitude |appName                 |
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.099861  | -80.2446  |Camel|

@CamelStoreListSearchByZip_Prod
Examples:
| Brand   | URL                                     | Username                                     | Password  | Latitude | Longitude |appName                 |
| CAMEL | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.099861  | -80.2446  |Camel|



    @CamelStoreDetailsMapView 
    Scenario Outline: Validate Grizzly Map View and Redeem Now Page
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I navigate to store list map view page
        When I select a store from map view
        Then I validate the user is on Redeem now page
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@CamelStoreDetailsMapView_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude | Longitude |appName                 |
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.099861  | -80.2446  |Camel|

@CamelStoreDetailsMapView_Prod
Examples:
| Brand   | URL                                     | Username                                     | Password  | Latitude | Longitude |appName                 |
| CAMEL | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.099861  |-80.2446  |Camel|


 @CamelStoreMapSearchByZip
    Scenario Outline: Validate GrizzlyMO Store Map View SearchBy valid and invalid Zip
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I navigate to store list map view page
        When I validate Search By zip with invalid zip code
        Then I validate the error message for invalid zipcode
        When I validate Search By zip with valid zip code
        Then I select a store from map view
        Then I validate the user is on Redeem now page
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@CamelStoreMapViewSearchByZip_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude | Longitude |appName                 |
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.099861  | -80.2446  |Camel|

@CamelStoreMapViewSearchByZip_Prod
Examples:
| Brand   | URL                                     | Username                                     | Password  | Latitude | Longitude |appName                 |
| CAMEL | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.099861  | -80.2446  |Camel|

 @CamelStoreDetailsMapView 
    Scenario Outline: Validate GrizzlyMO Map View Map it Page
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I navigate to store list map view page
        Then I validate the map view Page
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        

@CamelStoreDetailsMapViewMapIt_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude | Longitude |appName                 |
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.099861  | -80.2446  |Camel|

@CamelValidateStoreDetailsMapViewMapIt_Prod
Examples:
| Brand   | URL                                     | Username                                     | Password  | Latitude | Longitude |appName                 |
| CAMEL | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.099861  | -80.2446  |Camel|


@CamelSelect3rdStore 
    Scenario Outline: Validate GrizzlyMO StoreList Select 3rd Store and validate the error message
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I select 3rd store from store list page and validate the error message
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@CamelSelect3rdStore_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude   | Longitude    |appName                 |
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 37.3894698 | -121.9643038 |Camel|

@CamelSelect3rdStore_Prod
Examples:
| Brand   | URL                                     | Username                                     | Password  | Latitude   | Longitude    |appName                 |
| CAMEL | https://mobile.camel.com/ | <EMAIL> | Password1 | 37.3894698 | -121.9643038 |Camel|
