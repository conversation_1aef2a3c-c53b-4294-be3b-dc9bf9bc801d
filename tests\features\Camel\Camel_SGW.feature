Feature: Camel PWA - SGW Validation
    As a RJR user
    I want to access and validate the SGW text across all pages
    

    @CamelSGWValidation
    Scenario Outline: Validate Surgeon General Warning on the available pages
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>" 
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I validate that <PERSON><PERSON><PERSON> is present for Relevant Quater Q1 Q2 Q3 Q4 with SGW with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When I navigate to Hamburger Menu
        Then I validate that <PERSON><PERSON><PERSON> is present for Relevant Quater Q1 Q2 Q3 Q4 with SGW with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then I click HambergerMenu Close Button
        When I select a coupon and click on choose a store button
        Then I select a store from store list page
        When I click on the Redeem Now button
        Then I validate that <PERSON><PERSON><PERSON> is present for Relevant Quater Q1 Q2 Q3 Q4 with SGW with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        Then I click on the I'm Done button
        Then I validate that SGW is present for Relevant Quater Q1 Q2 Q3 Q4 with SGW with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
 
@CamelValidateSGW_QAstage
Examples:
| Brand     | URL                                          | Username                                | Password  | Latitude | Longitude | filename              | sheetname | scenarioname      |appName                 |
| CAMEL | https://mobile-qa.camel.com | <EMAIL> | Password1 | 42.7098  | -71.4437  | mobile-grizzlyMO.json | SGW_Text  | Validate SGW Text |Camel|
 
@CamelSGW_Prod
Examples:
| Brand     | URL                                    | Username                          | Password  | Latitude | Longitude | filename              | sheetname | scenarioname      |appName                 |
| CAMEL     | https://www.camel.com | <EMAIL> | Password1 | 42.7098  | -71.4437  | mobile-grizzlyMO.json | SGW_Text  | Validate SGW Text |Camel|


   