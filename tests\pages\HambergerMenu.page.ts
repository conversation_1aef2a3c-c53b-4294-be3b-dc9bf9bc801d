import { browser } from '@wdio/globals';
import Page from './Page.ts';
import elementActions from '../support/actions/element.actions.ts';
import HambergerMenuPageObjects from '../pageObjects/HamburgerMenu.pageObjects.ts';
import LoginPageObjects from '../pageObjects/Login.pageObjects.ts';
import assertionHelper from '../support/helpers/assertionHelper.ts';

class CouponHome extends Page {
    couponHomepageObject: any;



    public async validateElementGrizzlyHambergerMenu() {

        await elementActions.highlightElement(HambergerMenuPageObjects.grizzly_couponshmlink);  
         await expect(await HambergerMenuPageObjects.grizzly_couponshmlink).toBeDisplayed();   
         await elementActions.highlightElement(HambergerMenuPageObjects.grizzly_brandsitehmlink);  
         await expect(await HambergerMenuPageObjects.grizzly_brandsitehmlink).toBeDisplayed(); 
         await elementActions.highlightElement(HambergerMenuPageObjects.grizzly_logouthmlink);  
         await expect(await HambergerMenuPageObjects.grizzly_logouthmlink).toBeDisplayed(); 
         await expect(HambergerMenuPageObjects.brandIcon).toBeDisplayed();
         await expect(HambergerMenuPageObjects.grizzly_SGW_wrnig).toBeDisplayed();
         await expect(HambergerMenuPageObjects.closeHamMenu).toBeDisplayed();

    }

    public async verifyClickonGrizzlyCouponLink() {

        await elementActions.highlightElement(HambergerMenuPageObjects.grizzly_couponshmlink);     
        await elementActions.click(HambergerMenuPageObjects.grizzly_couponshmlink);
       
    }

    public async verifyClickonGrizzlyMobileSiteLink() {

        await elementActions.highlightElement(HambergerMenuPageObjects.grizzly_brandsitehmlink);     
        await elementActions.click(HambergerMenuPageObjects.grizzly_brandsitehmlink);  

    }

    public async verifyClickonGrizzlyLogoutLink(brand: string) {

        await elementActions.highlightElement(HambergerMenuPageObjects.grizzly_logouthmlink);     
        await elementActions.click(HambergerMenuPageObjects.grizzly_logouthmlink);
    
    }

    public async validateMobileSiteLinkAEMPage() {
        await browser.pause(2000);
        await elementActions.highlightElement(HambergerMenuPageObjects.brandIcon_AEMPage);    
        await expect(HambergerMenuPageObjects.brandIcon_AEMPage).toBeDisplayed();
        await expect(HambergerMenuPageObjects.grizzly_AEMPage_SGW_wrnig).toBeDisplayed();
        await expect(HambergerMenuPageObjects.aEMhomeButton).toBeDisplayed();
        await expect(HambergerMenuPageObjects.aEMlogoutButton).toBeDisplayed(); 
       
    }

    public async validateMobileSiteLinkAEMPageandLogout() {

        await elementActions.highlightElement(HambergerMenuPageObjects.brandIcon_AEMPage);    
        await expect(HambergerMenuPageObjects.brandIcon_AEMPage).toBeDisplayed();
        await expect(HambergerMenuPageObjects.grizzly_AEMPage_SGW_wrnig).toBeDisplayed();
        await expect(HambergerMenuPageObjects.aEMhomeButton).toBeDisplayed();
        await expect(HambergerMenuPageObjects.aEMlogoutButton).toBeDisplayed(); 
        await elementActions.click(HambergerMenuPageObjects.aEMlogoutButton);
        await expect(LoginPageObjects.usernameInput).toBeDisplayed();
        await expect(LoginPageObjects.passwordInput).toBeDisplayed();
        await expect(LoginPageObjects.loginButton).toBeDisplayed();

    }

    public async navigateToGrizzlyHambergerMenu() {
        await expect(await HambergerMenuPageObjects.hmbrgrMenu).toBeDisplayed(); 
        await elementActions.highlightElement(HambergerMenuPageObjects.hmbrgrMenu);     
        await elementActions.click(HambergerMenuPageObjects.hmbrgrMenu); 

    }

    public async clickCloseBtnHmMENU() {
        expect(await elementActions.isDisplayed(HambergerMenuPageObjects.closeHamMenu)).toBeTruthy();
        await elementActions.click(HambergerMenuPageObjects.closeHamMenu);
        await assertionHelper.assertElementDisplayed(HambergerMenuPageObjects.hmbrgrMenu);
 
    }


}





export default new CouponHome();