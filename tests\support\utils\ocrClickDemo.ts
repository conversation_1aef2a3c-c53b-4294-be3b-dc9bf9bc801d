import ocrClickUtil from './ocrClickUtil.ts';
import logger from './logger.util.ts';

/**
 * Demo utility to showcase proper OCR-based text clicking
 */
class OcrClickDemo {
  
  /**
   * Demonstrates clicking on specific text using OCR
   * @param textToFind - The text to find and click
   * @param caseSensitive - Whether search should be case sensitive
   */
  public async demonstrateOcrClick(textToFind: string, caseSensitive: boolean = false): Promise<void> {
    try {
      logger.info('=== OCR Click Demo Started ===');
      logger.info(`Looking for text: "${textToFind}"`);
      logger.info(`Case sensitive: ${caseSensitive}`);
      
      // First, let's see all text elements on the screen
      logger.info('Getting all text elements with bounding boxes...');
      const allTexts = await ocrClickUtil.getAllTextWithBoundingBoxes();
      
      logger.info(`Found ${allTexts.length} text elements on screen:`);
      allTexts.forEach((textElement, index) => {
        logger.info(`${index + 1}. "${textElement.text}" at (${textElement.boundingBox.x}, ${textElement.boundingBox.y}) - ${textElement.boundingBox.width}x${textElement.boundingBox.height}`);
      });
      
      // Now attempt to click on the specific text
      logger.info(`\nAttempting to click on "${textToFind}"...`);
      const success = await ocrClickUtil.clickOnText(textToFind, 3, 1000, caseSensitive);
      
      if (success) {
        logger.info(`✅ Successfully clicked on "${textToFind}"`);
      } else {
        logger.error(`❌ Failed to click on "${textToFind}"`);
      }
      
      logger.info('=== OCR Click Demo Completed ===');
      
    } catch (error) {
      logger.error(`Error in OCR click demo: ${error}`);
      throw error;
    }
  }
  
  /**
   * Finds and lists all text elements containing a specific substring
   * @param searchText - Text to search for
   * @param caseSensitive - Whether search should be case sensitive
   */
  public async findTextElements(searchText: string, caseSensitive: boolean = false): Promise<void> {
    try {
      logger.info('=== Text Search Demo Started ===');
      logger.info(`Searching for text containing: "${searchText}"`);
      logger.info(`Case sensitive: ${caseSensitive}`);
      
      const allTexts = await ocrClickUtil.getAllTextWithBoundingBoxes();
      const searchTextToUse = caseSensitive ? searchText : searchText.toLowerCase();
      
      const matchingTexts = allTexts.filter(textElement => {
        const textToCheck = caseSensitive ? textElement.text : textElement.text.toLowerCase();
        return textToCheck.includes(searchTextToUse);
      });
      
      logger.info(`Found ${matchingTexts.length} matching text elements:`);
      matchingTexts.forEach((textElement, index) => {
        logger.info(`${index + 1}. "${textElement.text}" at (${textElement.boundingBox.x}, ${textElement.boundingBox.y}) - ${textElement.boundingBox.width}x${textElement.boundingBox.height}`);
      });
      
      logger.info('=== Text Search Demo Completed ===');
      
    } catch (error) {
      logger.error(`Error in text search demo: ${error}`);
      throw error;
    }
  }
}

export default new OcrClickDemo();
