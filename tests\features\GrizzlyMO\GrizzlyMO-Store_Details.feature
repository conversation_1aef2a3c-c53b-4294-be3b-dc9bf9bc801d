Feature: GrizzlyMO PWA - Store Details
    As a RJR user
    I should be able access and validate the GrizzlyMO store list and Mapview
    So that I can ensure the Store Details functionality works correctly

    @GrizzlyStoreListView
    Scenario Outline: Validate GrizzlyMO coupon StoreList and LoadM<PERSON>ton
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I click on loadmore button and validate the list
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link

@GrizzlyMOStoreListView_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude | Longitude |appName                 |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|

@GrizzlyMOStoreListView_Prod
Examples:
| Brand   | URL                                      | Username                                     | Password  | Latitude | Longitude |appName                 |
| GRIZZLY | hhttps://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|


    @GrizzlyMOStoreListSearchByZip 
    Scenario Outline: Validate GrizzlyMO StoreList SearchBy valid and invalid Zip
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I validate Search By zip with invalid zip code
        Then I validate the error message for invalid zipcode
        When I validate Search By zip with valid zip code
        Then I select a store from store list page
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@GrizzlyMOStoreListSearchByZip_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude | Longitude |appName                 |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|

@GrizzlyMOStoreListSearchByZip_Prod
Examples:
| Brand   | URL                                     | Username                                     | Password  | Latitude | Longitude |appName                 |
| GRIZZLY | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|



    @GrizzlyMOStoreDetailsMapView 
    Scenario Outline: Validate Grizzly Map View and Redeem Now Page
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I navigate to store list map view page
        When I select a store from map view
        Then I validate the user is on Redeem now page
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@GrizzlyMOStoreDetailsMapView_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude | Longitude |appName                 |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|

@GrizzlyMOStoreDetailsMapView_Prod
Examples:
| Brand   | URL                                     | Username                                     | Password  | Latitude | Longitude |appName                 |
| GRIZZLY | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|


 @GrizzlyMOStoreMapSearchByZip
    Scenario Outline: Validate GrizzlyMO Store Map View SearchBy valid and invalid Zip
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I navigate to store list map view page
        When I validate Search By zip with invalid zip code
        Then I validate the error message for invalid zipcode
        When I validate Search By zip with valid zip code
        Then I select a store from map view
        Then I validate the user is on Redeem now page
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@GrizzlyMOStoreMapViewSearchByZip_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude | Longitude |appName                 |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|

@GrizzlyMOStoreMapViewSearchByZip_Prod
Examples:
| Brand   | URL                                     | Username                                     | Password  | Latitude | Longitude |appName                 |
| GRIZZLY | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|

 @GrizzlyMOStoreDetailsMapView 
    Scenario Outline: Validate GrizzlyMO Map View Map it Page
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I navigate to store list map view page
        Then I validate the map view Page
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        

@GrizzlyMOStoreDetailsMapViewMapIt_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude | Longitude |appName                 |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|

@GrizzlyMOValidateStoreDetailsMapViewMapIt_Prod
Examples:
| Brand   | URL                                     | Username                                     | Password  | Latitude | Longitude |appName                 |
| GRIZZLY | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|


@GrizzlyMOSelect3rdStore 
    Scenario Outline: Validate GrizzlyMO StoreList Select 3rd Store and validate the error message
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I select 3rd store from store list page and validate the error message
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@GrizzlyMOSelect3rdStore_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude   | Longitude    |appName                 |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 37.3894698 | -121.9643038 |Grizzly Nicotine Pouches|

@GrizzlyMOSelect3rdStore_Prod
Examples:
| Brand   | URL                                     | Username                                     | Password  | Latitude   | Longitude    |appName                 |
| GRIZZLY | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 37.3894698 | -121.9643038 |Grizzly Nicotine Pouches|
