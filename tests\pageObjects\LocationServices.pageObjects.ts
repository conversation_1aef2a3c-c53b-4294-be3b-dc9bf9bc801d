
class LocationServicesPageObjects {
    get understoodBtn() { return $('button[data-testid="understood-btn"]'); }
    get backBtn() { return $('button[data-testid="back-btn"]'); }

    get hamburgerMenu() { return $('[aria-label="hamburger menu"]'); }
    get couponMenuItem() { return $('a[title="Coupons"]'); }
    get myCouponMenuItem() { return $('a[title="My Coupons"]'); }
    get mobileBtn() { return $('//div[@class="cmp-offers__mobile-offers-cta-text"]'); }
    get couponReedemNowBtn() { return $('div[class="cmp-offers-mobile-only__offersAvailable active"] button'); }
    get backBtnLocation() { return $('button[data-testid="back-btn"]'); }
    get locationDisableHdr() { return $('//div[@data-testid="loc-disabled-text"]'); }
    get allowMessageLocPage() { return $('//div[@data-testid="allow-text"]'); }
    get importantTxt() { return $('//div[text()="IMPORTANT!"]'); }
    get playPauseBtn() { return $('//img[@data-testid="pause-play-btn"]'); }
    get androidLocationServiceAllowBtn() { return $('//android.widget.Button[@text="Allow while visiting the site"]'); }
    get iosLocationServicesNeverAllowBtn() { return $('~Don\'t Allow'); }
    get iOSLocationServiceAllowBtn() { return $('~Allow'); }
    get iOSSafariLocationAllowOnceBtn() { return $('~Allow Once'); }
    get iOSSfariLocationAllowWhileUsingAppBtn() { return $('~Allow While Using App'); }

    get redeemnowbtn() {return $("button[data-testid='altHomeRedeemNow']");}

    get locDisSc1Hdr() { return $('//div[contains(@class,"_important-text")]');}
    get locDisableAllowTxt() { return $('//div[contains(@class,"_allow-text")]');}
    get locSettingImg() { return $('//img[@data-testid="learn-loc-settings-img"]');}
    get learnHow() { return $('//div[@data-testid="learn-how-text"]');}
    get afterLocTxt() { return $('//div[contains(@class,"_after-enabled-text")]');}
    get shieldImage() { return $('//img[@alt="shield-image"]');}
    get continueBtn() { return $('//button[@data-testid="continue-btn"]');}
 
    get locDisPg2Hdr() { return $('//div[@data-testid="loc-disabled-text"]');}
    get headText() { return $('//div[@data-testid="head-text"]');}
    get locPermissionGuideImg() { return $('//img[@alt="location-permission-guide"]');}
    get scrollDownImg() { return $('//img[@alt="scroll-down-image"]');}
    get step1locDisPg2() { return $('(//*[contains(@class,"_instruction")]//p)[1]');}
    get step1locDisPg2DrpDwn1() { return $('(//img[@alt="arrow-image"])[1]');}
    get step2locDisPg2() { return $('(//*[contains(@class,"_instruction")]//p)[2]');}
    get step2locDisPg2DrpDwn2() { return $('(//img[@alt="arrow-image"])[2]');}
    get step3locDisPg2() { return $('(//*[contains(@class,"_instruction")]//p)[3]');}
    get step3locDisPg2DrpDwn3() { return $('(//img[@alt="arrow-image"])[3]');}
    get step4locDisPg2() { return $('(//*[contains(@class,"_instruction")]//p)[4]');}
    get step4locDisPg2DrpDwn4() { return $('(//img[@alt="arrow-image"])[4]');}
    get step5locDisPg3() { return $('(//*[contains(@class,"_instruction")]//p)[5]');}
    get step5locDisPg3DrpDwn5() { return $('(//img[@alt="arrow-image"])[5]');}
    get step6locDisPg3() { return $('(//*[contains(@class,"_instruction")]//p)[6]');}
    get step6locDisPg3DrpDwn6() { return $('(//img[@alt="arrow-image"])[6]');}
    get step7locDisPg5() { return $('(//*[contains(@class,"_instruction")]//p)[7]');}
    get step7locDisPg5DrpDwn7() { return $('(//img[@alt="arrow-image"])[7]');}
    get step1DrpDwnImg() { return $('(//img[@alt="instruction-image"])[1]');}
    get step2DrpDwnImg() { return $('(//img[@alt="instruction-image"])[2]');}
    get step3DrpDwnImg() { return $('(//img[@alt="instruction-image"])[3]');}
    get step4DrpDwnImg() { return $('(//img[@alt="instruction-image"])[4]');}
    get step5DrpDwnImg() { return $('(//img[@alt="instruction-image"])[5]');}
    get step6DrpDwnImg() { return $('(//img[@alt="instruction-image"])[6]');}
    get step7DrpDwnImg() { return $('(//img[@alt="instruction-image"])[7]');}
 
    get all3points() { return $('(//div[@class="_instruction_15yum_93"])[1] | (//div[@class="_instruction_15yum_93"])[2]) | (//div[@class="_instruction_15yum_93"])[3]');}
    get screengrabs() { return $('//div[@data-testid="screengrabs"]');}
    get afterLocTxtDisPg2() { return $('//*[contains(text(),"After the Location Services is enabled, select the button below.")]');}
 
    get lstPgHeadTxt() { return $('(//div[contains(@class,"first-try-text")]//div)');}
    get cstmrContanctMail() { return $('//*[@data-testid="contact-mail"]');}
    get cstmrContactNumber() { return $('//*[@data-testid="contact-num"]');}
    get tryAgainBtn() { return $('//*[@data-testid="try-again-btn"]');}
    get backToGrizzlyAemPage() { return $('//div[contains(@class,"_back-to-brand")]//div');}
    get Lnk_camelCopn() { return $('//a[@title="Offers"] | //a[@title="Coupons"]'); }
    get camelhmBergerMenu() { return $('//div[@class="hamburger-wrapper hamburger-menu-icon"] | //div[@class="hamburger-wrapper hamburger-menu-icon hamburger-wrapper--has-notifications"] | (//div[@class="hamburger-wrapper hamburger-menu-icon"])[1]'); }
    get camelRedeemNow() { return $('//div[contains(@class,"mobile-offers-cta")]'); }
    get aemHmMenu() { return $('//*[@class="mobile-nav-icon"]'); }
}

export default new LocationServicesPageObjects();
