import * as fs from 'fs';
import * as path from 'path';
import * as vision from '@google-cloud/vision';
import logger from './logger.util.ts';

/**
 * Interface for text detection result with bounding box
 */
export interface TextDetectionResult {
    text: string;
    boundingBox: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    confidence?: number;
}

export class GoogleVisionUtil {
    private credentialsPath: string;
    private client: vision.ImageAnnotatorClient;
 
    /**
     * Initialize Google Vision client with credentials
     * @param credentialsPath Optional path to custom credentials JSON file
     */
    constructor(credentialsPath?: string) {
        this.credentialsPath = credentialsPath || path.resolve(process.cwd(), './tests/resources/google-key/google-image-to-text-credentials.json');
        process.env.GOOGLE_APPLICATION_CREDENTIALS = this.credentialsPath;
        this.client = new vision.ImageAnnotatorClient();
        logger.info('Google Vision client initialized');
    }
 


    /**
     * Detects text in an image file
     * @param imagePath Path to the image file
     * @param deleteAfter Whether to delete the image after processing (default: false)
     * @returns Detected text as a string
     */
    public async detectText(imagePath: string, deleteAfter: boolean = false): Promise<string> {
        try {
            logger.info(`Detecting text in image: ${imagePath}`);

            if (!fs.existsSync(imagePath)) {
                throw new Error(`Image file not found: ${imagePath}`);
            }
            const imageFile = fs.readFileSync(imagePath);
            const [result] = await this.client.textDetection(imageFile);
            const textAnnotations = result.textAnnotations || [];

            let resultText = '';
            if (textAnnotations.length > 0) {
                resultText = textAnnotations[0].description || '';
                logger.info(`Text detected: ${resultText.substring(0, 50)}${resultText.length > 50 ? '...' : ''}`);
            } else {
                logger.info('No text detected in the image');
            }
            if (deleteAfter) {
                fs.unlinkSync(imagePath);
                logger.info(`Deleted image file: ${imagePath}`);
            }

            return resultText;
        } catch (error) {
            logger.error(`Error detecting text: ${error}`);
            throw error;
        }
    }

    /**
     * Detects text with bounding box information
     * @param imagePath Path to the image file
     * @param deleteAfter Whether to delete the image after processing (default: false)
     * @returns Array of text detection results with bounding boxes
     */
    public async detectTextWithBoundingBoxes(imagePath: string, deleteAfter: boolean = false): Promise<TextDetectionResult[]> {
        try {
            logger.info(`Detecting text with bounding boxes in image: ${imagePath}`);

            if (!fs.existsSync(imagePath)) {
                throw new Error(`Image file not found: ${imagePath}`);
            }
            const imageFile = fs.readFileSync(imagePath);
            const [result] = await this.client.textDetection(imageFile);
            const textAnnotations = result.textAnnotations || [];

            const detectionResults: TextDetectionResult[] = [];

            // Skip the first annotation as it contains the full text, process individual words/phrases
            for (let i = 1; i < textAnnotations.length; i++) {
                const annotation = textAnnotations[i];
                if (annotation.description && annotation.boundingPoly && annotation.boundingPoly.vertices) {
                    const vertices = annotation.boundingPoly.vertices;

                    // Calculate bounding box from vertices
                    const xCoords = vertices.map(v => v.x || 0);
                    const yCoords = vertices.map(v => v.y || 0);

                    const minX = Math.min(...xCoords);
                    const maxX = Math.max(...xCoords);
                    const minY = Math.min(...yCoords);
                    const maxY = Math.max(...yCoords);

                    detectionResults.push({
                        text: annotation.description,
                        boundingBox: {
                            x: minX,
                            y: minY,
                            width: maxX - minX,
                            height: maxY - minY,
                        },
                        confidence: annotation.confidence ?? undefined,
                    });
                }
            }

            logger.info(`Detected ${detectionResults.length} text elements with bounding boxes`);

            if (deleteAfter) {
                fs.unlinkSync(imagePath);
                logger.info(`Deleted image file: ${imagePath}`);
            }

            return detectionResults;
        } catch (error) {
            logger.error(`Error detecting text with bounding boxes: ${error}`);
            throw error;
        }
    }

    /**
     * Finds text elements that contain the specified text
     * @param imagePath Path to the image file
     * @param searchText Text to search for
     * @param caseSensitive Whether search should be case sensitive (default: false)
     * @param deleteAfter Whether to delete the image after processing (default: false)
     * @returns Array of matching text detection results
     */
    public async findTextWithBoundingBoxes(
        imagePath: string,
        searchText: string,
        caseSensitive: boolean = false,
        deleteAfter: boolean = false,
    ): Promise<TextDetectionResult[]> {
        const allDetections = await this.detectTextWithBoundingBoxes(imagePath, deleteAfter);

        const searchTextToUse = caseSensitive ? searchText : searchText.toLowerCase();

        return allDetections.filter(detection => {
            const textToCheck = caseSensitive ? detection.text : detection.text.toLowerCase();
            return textToCheck.includes(searchTextToUse);
        });
    }
 
    /**
     * Takes a screenshot of an element or page and detects text in it
     * @param element WebdriverIO element or selector string
     * @param screenshotName Optional name for the screenshot file
     * @returns Detected text as a string
     */
    public async detectTextFromElement(
        element: ChainablePromiseElement,
        screenshotName?: string,
    ): Promise<string> {
        try {
            const timestamp = new Date().getTime();
            const fileName = screenshotName || `screenshot_${timestamp}.png`;
            const screenshotPath = path.resolve(process.cwd(), 'reports/screenshots', fileName);
 
            const dir = path.dirname(screenshotPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
 
            if (element) {
                if (typeof element === 'string') {
                    logger.info(`Taking screenshot of element with selector: ${element}`);
                    await $(element).saveScreenshot(screenshotPath);
                } else {
                    logger.info('Taking screenshot of provided element');
                    await element.saveScreenshot(screenshotPath);
                }
            } else {
                logger.info('Taking full page screenshot');
                await browser.saveScreenshot(screenshotPath);
            }
 
            const text = await this.detectText(screenshotPath, true);
            return text;
        } catch (error) {
            logger.error(`Error in detectTextFromElement: ${error}`);
            throw error;
        }
    }
 
    /**
     * Normalizes text by removing extra whitespace and line breaks
     * @param text Text to normalize
     * @returns Normalized text
     */
    public normalizeText(text: string): string {
        return text
            .replace(/\r\n/g, ' ')  // Replace Windows line breaks with spaces
            .replace(/\n/g, ' ')    // Replace Unix line breaks with spaces
            .replace(/\s+/g, ' ')   // Replace multiple spaces with a single space
            .trim();                // Remove leading/trailing whitespace
    }
 
    /**
     * Detects text from element and normalizes it to a single line
     * @param element WebdriverIO element or selector string
     * @param screenshotName Optional screenshot filename
     * @returns Normalized single-line text
     */
    public async detectTextFromElementSingleLine(
        element: ChainablePromiseElement,
        screenshotName?: string,
    ): Promise<string> {
        const text = await this.detectTextFromElement(element, screenshotName);
        return this.normalizeText(text);
    }
 
    /**
     * Extracts only alphanumeric characters and spaces from text
     * @param text Text to process
     * @returns Alphanumeric text
     */
    public alphanumericOnly(text: string): string {
        return text.replace(/[^a-zA-Z0-9\s]/g, '').trim();
    }
}
 
export default GoogleVisionUtil;