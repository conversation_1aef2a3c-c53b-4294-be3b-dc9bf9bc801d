

class LoginPageObjects {
    get usernameInput() { return $('[name="email"]') }
    get passwordInput() { return $('[name="password"]'); }
    get loginButton() { return $('#loginButton'); }
    get postLoginLogo() { return $('.cmp-header__logo-link'); }
    get rememberMeCheckbox() { return $('img[alt="Unchecked Box"]'); }
    get loginErrorMsg() { return $('#modalMessage'); }
    get logoutBtn() { return $('span*=Logout'); }


    get rememberedcheckbox() {return $('img[alt="Checked box"]') }

    get hmBurgerMenu() { return $('//div[@aria-label="hamburger menu"]');}
    get myProfileHmMenu() { return $('//a[@title="My Profile"]');}
    get logoutMyProfile() { return $('//a[text()="Logout"]');}

    get chromeMenuBtn() { return $('android=new UiSelector().resourceId("com.android.chrome:id/menu_button")'); }
    get chromeInstallBtn() { return $('android=new UiSelector().resourceId("com.android.chrome:id/arrow_install")'); }
    get chromeConfirmInstallBtn() { return $('android=new UiSelector().resourceId("com.android.chrome:id/positive_button")'); }
    get chromeAddToHomeScreenBtn() { return $('android=new UiSelector().resourceId("com.android.chrome:id/menu_item_text").textContains("Add")'); }
    get chromePopUpAddToHomeScreenLayover() { return $('android=new UiSelector().resourceId("com.google.android.apps.nexuslauncher:id/actions_container")'); }
    get chromPopUpAddToHomeScreenBtn() { return $('android=new UiSelector().className("android.widget.Button").text("Add to home screen")'); }
    get iosShareBtn() { return $('~ShareButton'); }
    get iosAddToHomeBtn() { return $('-ios class chain:**/XCUIElementTypeStaticText[`label == "Add to Home Screen"`]'); }
    get iosAddBtn() { return $('~Add'); }

    
}

export default new LoginPageObjects();
