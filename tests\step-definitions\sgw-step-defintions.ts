import { Given, When, Then } from '@wdio/cucumber-framework';
import logger from '../support/utils/logger.util.ts';
import { expect as expectChai } from 'chai';
import mobileActions from '../support/actions/mobile.actions.ts';
import loginPage from '../pages/login.page.ts';
import sgwPage from '../pages/sgw.page.ts';
//import sgwPage from '../pages/sgw.page .ts';
 
Then(/^I validate that SGW is present for Relevant Quater Q1 Q2 Q3 Q4 with SGW with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath :string,sheetname :string, scenarioname :string) {
    await sgwPage.compareSQWText(filepath,sheetname,scenarioname);
    console.log('Navigated to Hamburger Menu and click Logout');
    await logger.info('Navigated to Hamburger Menu and click Logout');
});