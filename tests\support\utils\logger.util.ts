// test/support/utils/logger.util.ts
import winston from 'winston';

class Logger {
    debug(_arg0: string) {
        throw new Error('Method not implemented.');
    }
    private logger: winston.Logger;
    private static instance: Logger;

    private constructor() {
        this.logger = winston.createLogger({
            level: 'info',
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.printf(({ timestamp, level, message, sessionId, ...metadata }) => {
                    // Include Saucelabs session ID in logs for easy correlation
                    const sessionInfo = sessionId ? `[Session: ${sessionId}] ` : '';
                    const metadataStr = Object.keys(metadata).length ?
                        ` | ${JSON.stringify(metadata)}` : '';
                    return `${timestamp} ${sessionInfo}[${level}]: ${message}${metadataStr}`;
                }),
            ),
            transports: [
                new winston.transports.Console({
                    format: winston.format.colorize({ all: true }),
                }),
            ],
        });
    }

    public static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }

    private async getSessionInfo() {
        const sessionId = browser.sessionId;
        const capabilities = browser.capabilities as Record<string, any>;
        // Safely access Sauce Labs job ID
        const sauceJobId = capabilities['sauce:options']?.['job-name'] || capabilities['sauce:job']?.id;
        return {
            sessionId,
            capabilities,
            sauceJobId,
        };
    }



    async info(message: string, metadata: object = {}): Promise<void> {
        const sessionInfo = await this.getSessionInfo();
        this.logger.info(message, { ...sessionInfo, ...metadata });
    }

    async error(message: string | Error, metadata: object = {}): Promise<void> {
        const sessionInfo = await this.getSessionInfo();
        const errorMessage = message instanceof Error ? message.message : message;
        const stackTrace = message instanceof Error ? message.stack : undefined;

        this.logger.error(errorMessage, {
            ...sessionInfo,
            ...metadata,
            stackTrace,
        });

        // If we have a Saucelabs session, mark the test as failed
        if (browser?.execute && sessionInfo.sauceJobId) {
            await browser.execute('sauce:job-result=failed');
        }
    }
}

export default Logger.getInstance();
