export interface INavigationOptions {
    timeout?: number;
    waitUntil?: () => Promise<boolean>;
}

export interface IElementActionOptions {
    timeout?: number;
    force?: boolean;
}

export interface IGestureOptions {
    percentageX?: number;
    percentageY?: number;
    duration?: number;
    timeout?: number;
    interval?: number;
    timeoutMsg?: string;
}
export interface IWaitOptions {
    timeout?: number;
    interval?: number;
    reverse?: boolean;
    timeoutMsg?: string;
    error?: Error;
}

export interface IElementWaitState {
    isDisplayed: boolean;
    isEnabled: boolean;
    isClickable?: boolean;
    isAccessible?: boolean;
}

export type SwipeDirection = 'up' | 'down' | 'left' | 'right';
export type ScrollDirection = 'up' | 'down';
export type OrientationType = 'PORTRAIT' | 'LANDSCAPE';
