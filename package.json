{"name": "pwa-webdriverio-cucumber", "type": "module", "engines": {"node": "^16.13 || >=18"}, "scripts": {"lint": "eslint .", "sauce-visual": "wdio run ./tests/configs/wdio.saucelabs.desktop.conf.ts --spec tests/features/inventory.feature", "sauce-visual-mobile": "wdio run ./tests/configs/wdio.saucelabs.mobile.conf.ts --spec tests/features/inventory.feature", "sauce-visual-check": "VISUAL_CHECK=true wdio run ./tests/configs/wdio.saucelabs.desktop.conf.ts --spec tests/features/inventory.feature", "sauce-visual-check-mobile": "VISUAL_CHECK=true wdio run ./tests/configs/wdio.saucelabs.mobile.conf.ts --spec tests/features/inventory.feature", "wdio": "wdio run ./wdio.conf.ts", "clean:reports": "rm -rf allure-results/* reports/*", "allure:generate": "allure generate reports/allure-results --clean -o reports/allure-report", "allure:open": "allure open reports/allure-report", "report": "npm run allure:generate && npm run allure:open", "test-camel-native": "wdio run ./tests/configs/wdio.saucelabs.mobile-native.conf.ts --spec tests/features/camel-native.feature", "test-camel-spa": "wdio run ./tests/configs/wdio.saucelabs.mobile-SPA.conf.ts --spec tests/features/coupon-home.feature", "test-grizzlyMO-pwa": "wdio run ./tests/configs/wdio.saucelabs.mobile-pwa.conf.ts", "test-grizzly-addshortcut": "wdio run ./tests/configs/wdio.saucelabs.mobile-pwa.conf.ts --spec tests/features/GrizzlyMO/GrizzlyMO_AddingShortCut.feature", "test-camel-pwa": "wdio run ./tests/configs/wdio.saucelabs.mobile-pwa.conf.ts --spec tests/features/camel-pwa.feature"}, "devDependencies": {"@eslint/js": "^9.29.0", "@google-cloud/vision": "^5.2.0", "@saucelabs/wdio-sauce-visual-service": "^0.13.0", "@types/chai": "^5.2.2", "@types/jest": "^30.0.0", "@types/mssql": "^9.1.7", "@types/readline-sync": "^1.4.8", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@wdio/allure-reporter": "^9.16.2", "@wdio/appium-service": "^9.16.2", "@wdio/cli": "9.16.2", "@wdio/cucumber-framework": "^9.16.2", "@wdio/devtools-service": "^7.16.4", "@wdio/globals": "^9.16.2", "@wdio/json-reporter": "^9.16.2", "@wdio/junit-reporter": "^9.16.2", "@wdio/local-runner": "^9.16.2", "@wdio/runner": "^9.16.2", "@wdio/sauce-service": "^9.16.2", "@wdio/spec-reporter": "^9.16.2", "allure-commandline": "^2.34.1", "chai": "^5.2.0", "date-fns": "^4.1.0", "dotenv": "^16.6.0", "eslint": "^9.29.0", "eslint-plugin-cucumber": "^2.0.0", "eslint-plugin-wdio": "^9.16.2", "exceljs": "^4.4.0", "gmail-tester": "^1.3.8", "mssql": "^11.0.1", "readline-sync": "^1.4.10", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "update-dotenv": "^1.1.1", "wdio-gmail-service": "^2.1.0", "wdio-html-nice-reporter": "^8.1.7", "wdio-wait-for": "^3.1.0", "winston": "^3.17.0"}, "dependencies": {"allure": "^0.0.0", "opencage-api-client": "^1.1.0", "xlsx": "^0.18.5"}}