Feature: Camel PWA - Alt Home Page Validation
    As a RJR user
    I want to access and validate the Alt Home Page
    So that I can ensure the Alt-Home functionality works correctly

    @CamelValidateAlthome
    Scenario Outline: Validate Camel Alt Home page and the navigation to the
        Given I am on the <Brand> login page at <URL>
        When I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        Then I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I validate the Alt Home Page for the brand <Brand>
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        Then I validate the MobileSite Link Aem Page and click Logout

        @CamelValidateAlthome_QA
        Examples:
            | Brand | URL                          | Username                                     | Password  | Latitude          | Longitude          | appName |
            | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | Camel   |

        @CamelValidateAlthome_Prod
        Examples:
            | Brand | URL                           | Username                                     | Password  | Latitude          | Longitude          | appName |
            | CAMEL | https://www.mobile.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | Camel   |


