import { browser } from '@wdio/globals';
import Page from './Page.ts';
import elementActions from '../support/actions/element.actions.ts';
import locationServicesPageObjects from '../pageObjects/LocationServices.pageObjects.ts';
import CouponHomePageObject from '../pageObjects/CouponHome.pageObject.ts';
import AltHomePageObjects from '../pageObjects/AltHome.pageObjects.ts';
import assertionHelper from '../support/helpers/assertionHelper.ts';
import { JsonTestDataHandler } from '../support/utils/JsonTestDataHandler.ts';
import path from 'path';
import HamburgerMenuPageObjects from '../pageObjects/HamburgerMenu.pageObjects.ts';

class LocationServices extends Page {

  async navigateToActiveCouponPage() {
    await elementActions.click(locationServicesPageObjects.understoodBtn);
    await driver.switchContext('NATIVE_APP');
    if (driver.isAndroid) {
      const allowloc = locationServicesPageObjects.androidLocationServiceAllowBtn;
      await (await locationServicesPageObjects.androidLocationServiceAllowBtn).click();
      await browser.switchContext('CHROMIUM');
      
    } else {
      const locAllowPopUp = await locationServicesPageObjects.iOSSafariLocationAllowOnceBtn;
      const allowPopupDispalyed = await locAllowPopUp.isDisplayed();
      if (allowPopupDispalyed) {
        await elementActions.click(locationServicesPageObjects.iOSSafariLocationAllowOnceBtn);
      }
      //const pwalocallowpop = await locationServicesPageObjects.iOSLocationServiceAllowBtn;
      //const allowpwapopupdisplayed= await pwalocallowpop.isDisplayed();
      if(await elementActions.isElementPresent(locationServicesPageObjects.iOSLocationServiceAllowBtn))
      {
        await elementActions.click(locationServicesPageObjects.iOSLocationServiceAllowBtn);
      }
       
      
      //await elementActions.click(locationServicesPageObjects.iOSLocationServiceAllowBtn);
      const contexts = await driver.getContexts();
      const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
      if (webviewContext) {
        await driver.switchContext(webviewContext.toString());
      }
      if(await elementActions.isElementPresent(AltHomePageObjects.btnpushnotificationsallow))
        {
          await elementActions.click(AltHomePageObjects.btnpushnotificationsallow);
        }
    }
    //Should Available but remove locationServicesPageObjects line for another Reason
   // await elementActions.waitForDisplayed(couponHomePage.brandLogo);
    //expect(await couponHomePage.brandLogo).toBeDisplayed();
  }

    /*async navigateToActiveCouponPage() {
      await (await locationServicesPageObjects.understoodBtn).click();
      await driver.switchContext("NATIVE_APP");
      if (await driver.isAndroid) {
        await (await locationServicesPageObjects.androidLocationServiceAllowBtn).click();
        await driver.switchContext("CHROMIUM");
      } else {
  
        await elementActions.clickElementIfExistsAndVisible('~Allow Once');
        await elementActions.clickElementIfExistsAndVisible( `~Allow`);
        const contexts = await driver.getContexts();
        //await driver.switchContext(contexts.find(context => context.toString().includes('WEBVIEW')).toString());
      const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
      if (webviewContext) {
        await driver.switchContext(webviewContext.toString());
      }
      }
  
      await elementActions.waitForDisplayed(CouponHomePageObject.brandLogo);
      expect(await CouponHomePageObject.brandLogo).toBeDisplayed();
    }*/
  
  
    public async navigateToOffersPage() {
    ;
    await elementActions.click(locationServicesPageObjects.hamburgerMenu);
    await elementActions.click(locationServicesPageObjects.couponMenuItem);
    await elementActions.waitForClickable(locationServicesPageObjects.couponReedemNowBtn);
    await expect(await locationServicesPageObjects.couponReedemNowBtn).toBeDisplayed();
  }


  public async clickUnderStoodButtonWithoutAllowLocation() {
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.importantTxt)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.allowMessageLocPage)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.playPauseBtn)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.backBtnLocation)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.understoodBtn)).toBeTruthy();
    await elementActions.click(locationServicesPageObjects.understoodBtn);
    await driver.switchContext('NATIVE_APP');
    if (await driver.isAndroid) {
      // Android specific handling would go here
    } else {
      const locAllowPopUp = await locationServicesPageObjects.iosLocationServicesNeverAllowBtn;
      const allowPopupDispalyed = await locAllowPopUp.isDisplayed();
      if (allowPopupDispalyed) {
        await elementActions.click(locationServicesPageObjects.iosLocationServicesNeverAllowBtn);
      }
      else {
        await driver.dismissAlert();
      }
      const contexts = await driver.getContexts();
      const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
      if (webviewContext) {
        await driver.switchContext(webviewContext.toString());
      }
    }
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locationDisableHdr)).toBeTruthy();
  }

  
  public async navigateToGrizzlyOffersPage() {
    await expect(await locationServicesPageObjects.mobileBtn).toBeDisplayed();
    await elementActions.click(locationServicesPageObjects.mobileBtn);
  }


  public async clickBackBtnLocationServicesPage() {
    await elementActions.highlightElement(locationServicesPageObjects.importantTxt);
    await expect(await locationServicesPageObjects.importantTxt).toBeDisplayed();

    await elementActions.highlightElement(locationServicesPageObjects.allowMessageLocPage); 
    await expect(await locationServicesPageObjects.allowMessageLocPage).toBeDisplayed();

    await elementActions.highlightElement(locationServicesPageObjects.playPauseBtn); 
    await expect(await locationServicesPageObjects.playPauseBtn).toBeDisplayed();
    
    await elementActions.highlightElement(locationServicesPageObjects.backBtnLocation); 
    await expect(await locationServicesPageObjects.backBtnLocation).toBeDisplayed();
    await expect(await locationServicesPageObjects.backBtn).toBeDisplayed();
    await elementActions.click(locationServicesPageObjects.backBtn);
    await browser.pause(2000);
  }

  public async navigateToLocationServicesPage() {
    await elementActions.click(locationServicesPageObjects.couponReedemNowBtn);
    await elementActions.waitForClickable(locationServicesPageObjects.understoodBtn);
    await expect(await locationServicesPageObjects.understoodBtn).toBeDisplayed();
  }

  async locationPageValidation(filename: string, sheetname: string, scenarioname: string) {
    const SHEET_NAME = sheetname;
    const jsonFilePath = path.join(process.cwd(), 'data', filename);
    const testData = new JsonTestDataHandler(jsonFilePath);
    const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
    console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
    const understoodText = testData.getCellValue(SHEET_NAME, scenarioname, 'understoodText');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.understoodBtn, understoodText);
    const importantText = testData.getCellValue(SHEET_NAME, scenarioname, 'importantText');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.importantTxt, importantText);
    const backBtnText = testData.getCellValue(SHEET_NAME, scenarioname, 'backBtnText');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.backBtnLocation, backBtnText);
    const allowLocMessageTet = testData.getCellValue(SHEET_NAME, scenarioname, 'allowText');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.allowMessageLocPage, allowLocMessageTet);
 
  }

  async locationDisableScreen1(filename: string, sheetname: string, scenarioname: string) {
    const SHEET_NAME = sheetname;
    const jsonFilePath = path.join(process.cwd(), 'data', filename);
    const testData = new JsonTestDataHandler(jsonFilePath);
    const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
    console.log(`User Data for TC02: ${JSON.stringify(userData1)}`);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisSc1Hdr)).toBeTruthy();
    const locDisableHeaderPg1 = testData.getCellValue(SHEET_NAME, scenarioname, 'locationDisabledTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.locDisSc1Hdr, locDisableHeaderPg1);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisableAllowTxt)).toBeTruthy();
    const locDisableAllowText = testData.getCellValue(SHEET_NAME, scenarioname, 'locationEnableTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.locDisableAllowTxt, locDisableAllowText);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locSettingImg)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.learnHow)).toBeTruthy();
    const learnHowText = testData.getCellValue(SHEET_NAME, scenarioname, 'learnHowTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.learnHow, learnHowText);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.afterLocTxt)).toBeTruthy();
    const afterLocationText = testData.getCellValue(SHEET_NAME, scenarioname, 'afterEnableTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.afterLocTxt, afterLocationText);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.shieldImage)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.continueBtn)).toBeTruthy();
    const continueBtnText = testData.getCellValue(SHEET_NAME, scenarioname, 'continueBtn');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.continueBtn, continueBtnText);
    await elementActions.click(locationServicesPageObjects.continueBtn);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
 
 
 
  }
 
  async locationDisableScreen2(filename: string, sheetname: string, scenarioname: string) {
    const SHEET_NAME = sheetname;
    const jsonFilePath = path.join(process.cwd(), 'data', filename);
    const testData = new JsonTestDataHandler(jsonFilePath);
    const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
    console.log(`User Data for TC02: ${JSON.stringify(userData1)}`);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
    const locDisableHdrPg2 = testData.getCellValue(SHEET_NAME, scenarioname, 'locationDisabledTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.locDisPg2Hdr, locDisableHdrPg2);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.headText)).toBeTruthy();
    const locDisbleLetsTryTxtPg2 = testData.getCellValue(SHEET_NAME, scenarioname, 'let\'sTryTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.headText, locDisbleLetsTryTxtPg2);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locPermissionGuideImg)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.playPauseBtn)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.scrollDownImg)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.shieldImage)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step1locDisPg2)).toBeTruthy();
    const locDisableSc2Step1 = testData.getCellValue(SHEET_NAME, scenarioname, 'step1LocDisPg2');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step1locDisPg2, locDisableSc2Step1);
    await elementActions.click(locationServicesPageObjects.step1locDisPg2DrpDwn1);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step1DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step2locDisPg2)).toBeTruthy();
    const locDisableSc2Step2 = testData.getCellValue(SHEET_NAME, scenarioname, 'step2LocDisPg2');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step2locDisPg2, locDisableSc2Step2);
    await elementActions.click(locationServicesPageObjects.step2locDisPg2DrpDwn2);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step2DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step3locDisPg2)).toBeTruthy();
    const locDisableSc2Step3 = testData.getCellValue(SHEET_NAME, scenarioname, 'step3LocDisPg2');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step3locDisPg2, locDisableSc2Step3);
    await elementActions.click(locationServicesPageObjects.step3locDisPg2DrpDwn3);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step3DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step4locDisPg2)).toBeTruthy();
    const locDisableSc2Step4 = testData.getCellValue(SHEET_NAME, scenarioname, 'step4LocDisPg2');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step4locDisPg2, locDisableSc2Step4);
    await elementActions.click(locationServicesPageObjects.step4locDisPg2DrpDwn4);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step4DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.screengrabs)).toBeTruthy();
    const screenGrabs = testData.getCellValue(SHEET_NAME, scenarioname, 'screengrabsTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.screengrabs, screenGrabs);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.afterLocTxtDisPg2)).toBeTruthy();
    const afterLocationDisPg2Text = testData.getCellValue(SHEET_NAME, scenarioname, 'afterEnableTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.afterLocTxtDisPg2, afterLocationDisPg2Text);
 
    const continueBtnText = testData.getCellValue(SHEET_NAME, scenarioname, 'continueBtn');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.continueBtn, continueBtnText);
    await elementActions.click(locationServicesPageObjects.continueBtn);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
 
  }
 
 
  async locationDisableScreen3(filename: string, sheetname: string, scenarioname: string) {
    const SHEET_NAME = sheetname;
    const jsonFilePath = path.join(process.cwd(), 'data', filename);
    const testData = new JsonTestDataHandler(jsonFilePath);
    const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
    console.log(`User Data for TC02: ${JSON.stringify(userData1)}`);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
    const locDisableHdrPg2 = testData.getCellValue(SHEET_NAME, scenarioname, 'locationDisabledTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.locDisPg2Hdr, locDisableHdrPg2);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.headText)).toBeTruthy();
    const locDisbleLetsTryTxtPg2 = testData.getCellValue(SHEET_NAME, scenarioname, 'let\'sTryTxtPg3');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.headText, locDisbleLetsTryTxtPg2);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locPermissionGuideImg)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.playPauseBtn)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.scrollDownImg)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.shieldImage)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step1locDisPg2)).toBeTruthy();
    const locDisableSc2Step1 = testData.getCellValue(SHEET_NAME, scenarioname, 'step1LocDisPg2');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step1locDisPg2, locDisableSc2Step1);
    await elementActions.click(locationServicesPageObjects.step1locDisPg2DrpDwn1);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step1DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step2locDisPg2)).toBeTruthy();
    const locDisableSc2Step2 = testData.getCellValue(SHEET_NAME, scenarioname, 'step2LocDisPg2');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step2locDisPg2, locDisableSc2Step2);
    await elementActions.click(locationServicesPageObjects.step2locDisPg2DrpDwn2);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step2DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step3locDisPg2)).toBeTruthy();
    const locDisableSc2Step3 = testData.getCellValue(SHEET_NAME, scenarioname, 'step3LocDisPg2');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step3locDisPg2, locDisableSc2Step3);
    await elementActions.click(locationServicesPageObjects.step3locDisPg2DrpDwn3);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step3DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step4locDisPg2)).toBeTruthy();
    const locDisableSc2Step4 = testData.getCellValue(SHEET_NAME, scenarioname, 'step4LocDisPg2');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step4locDisPg2, locDisableSc2Step4);
    await elementActions.click(locationServicesPageObjects.step4locDisPg2DrpDwn4);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step4DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step5locDisPg3)).toBeTruthy();
    const locDisableSc3Step5 = testData.getCellValue(SHEET_NAME, scenarioname, 'step5LocDisPg3');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step5locDisPg3, locDisableSc3Step5);
    await elementActions.click(locationServicesPageObjects.step5locDisPg3DrpDwn5);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step5DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step6locDisPg3)).toBeTruthy();
    const locDisableSc3Step6 = testData.getCellValue(SHEET_NAME, scenarioname, 'step6LocDisPg3');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step6locDisPg3, locDisableSc3Step6);
    await elementActions.click(locationServicesPageObjects.step6locDisPg3DrpDwn6);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step6DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.screengrabs)).toBeTruthy();
    const screenGrabs = testData.getCellValue(SHEET_NAME, scenarioname, 'screengrabsTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.screengrabs, screenGrabs);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.afterLocTxtDisPg2)).toBeTruthy();
    const afterLocationDisPg2Text = testData.getCellValue(SHEET_NAME, scenarioname, 'afterEnableTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.afterLocTxtDisPg2, afterLocationDisPg2Text);
 
    const continueBtnText = testData.getCellValue(SHEET_NAME, scenarioname, 'continueBtn');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.continueBtn, continueBtnText);
    await elementActions.click(locationServicesPageObjects.continueBtn);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
 
  }
 
  async locationDisableScreen4(filename: string, sheetname: string, scenarioname: string) {
    const SHEET_NAME = sheetname;
    const jsonFilePath = path.join(process.cwd(), 'data', filename);
    const testData = new JsonTestDataHandler(jsonFilePath);
    const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
    console.log(`User Data for TC02: ${JSON.stringify(userData1)}`);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
    const locDisableHdrPg2 = testData.getCellValue(SHEET_NAME, scenarioname, 'locationDisabledTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.locDisPg2Hdr, locDisableHdrPg2);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.headText)).toBeTruthy();
    const locDisbleLetsTryTxtPg2 = testData.getCellValue(SHEET_NAME, scenarioname, 'locationHeadTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.headText, locDisbleLetsTryTxtPg2);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locPermissionGuideImg)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.playPauseBtn)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.scrollDownImg)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.shieldImage)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step1locDisPg2)).toBeTruthy();
    const locDisableSc2Step1 = testData.getCellValue(SHEET_NAME, scenarioname, 'step1LocDisPg4');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step1locDisPg2, locDisableSc2Step1);
    await elementActions.click(locationServicesPageObjects.step1locDisPg2DrpDwn1);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step1DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step2locDisPg2)).toBeTruthy();
    const locDisableSc2Step2 = testData.getCellValue(SHEET_NAME, scenarioname, 'step2LocDisPg4');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step2locDisPg2, locDisableSc2Step2);
    await elementActions.click(locationServicesPageObjects.step2locDisPg2DrpDwn2);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step2DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step3locDisPg2)).toBeTruthy();
    const locDisableSc2Step3 = testData.getCellValue(SHEET_NAME, scenarioname, 'step3LocDisPg4');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step3locDisPg2, locDisableSc2Step3);
    await elementActions.click(locationServicesPageObjects.step3locDisPg2DrpDwn3);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step3DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step4locDisPg2)).toBeTruthy();
    const locDisableSc2Step4 = testData.getCellValue(SHEET_NAME, scenarioname, 'step4LocDisPg4');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step4locDisPg2, locDisableSc2Step4);
    await elementActions.click(locationServicesPageObjects.step4locDisPg2DrpDwn4);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step4DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.screengrabs)).toBeTruthy();
    const screenGrabs = testData.getCellValue(SHEET_NAME, scenarioname, 'screengrabsTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.screengrabs, screenGrabs);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.afterLocTxtDisPg2)).toBeTruthy();
    const afterLocationDisPg2Text = testData.getCellValue(SHEET_NAME, scenarioname, 'afterEnableTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.afterLocTxtDisPg2, afterLocationDisPg2Text);
 
    const continueBtnText = testData.getCellValue(SHEET_NAME, scenarioname, 'continueBtn');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.continueBtn, continueBtnText);
    await elementActions.click(locationServicesPageObjects.continueBtn);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
 
  }
 
  async locationDisableScreen5(filename: string, sheetname: string, scenarioname: string) {
    const SHEET_NAME = sheetname;
    const jsonFilePath = path.join(process.cwd(), 'data', filename);
    const testData = new JsonTestDataHandler(jsonFilePath);
    const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
    console.log(`User Data for TC02: ${JSON.stringify(userData1)}`);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
    const locDisableHdrPg2 = testData.getCellValue(SHEET_NAME, scenarioname, 'locationDisabledTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.locDisPg2Hdr, locDisableHdrPg2);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.headText)).toBeTruthy();
    const locDisbleLetsTryTxtPg2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lastTryTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.headText, locDisbleLetsTryTxtPg2);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locPermissionGuideImg)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.playPauseBtn)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.scrollDownImg)).toBeTruthy();
    expect(await elementActions.isDisplayed(locationServicesPageObjects.shieldImage)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step1locDisPg2)).toBeTruthy();
    const locDisableSc2Step1 = testData.getCellValue(SHEET_NAME, scenarioname, 'step1LocDisPg5');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step1locDisPg2, locDisableSc2Step1);
    await elementActions.click(locationServicesPageObjects.step1locDisPg2DrpDwn1);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step1DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step2locDisPg2)).toBeTruthy();
    const locDisableSc2Step2 = testData.getCellValue(SHEET_NAME, scenarioname, 'step2LocDisPg5');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step2locDisPg2, locDisableSc2Step2);
    await elementActions.click(locationServicesPageObjects.step2locDisPg2DrpDwn2);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step2DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step3locDisPg2)).toBeTruthy();
    const locDisableSc2Step3 = testData.getCellValue(SHEET_NAME, scenarioname, 'step3LocDisPg5');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step3locDisPg2, locDisableSc2Step3);
    await elementActions.click(locationServicesPageObjects.step3locDisPg2DrpDwn3);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step3DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step4locDisPg2)).toBeTruthy();
    const locDisableSc2Step4 = testData.getCellValue(SHEET_NAME, scenarioname, 'step4LocDisPg5');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step4locDisPg2, locDisableSc2Step4);
    await elementActions.click(locationServicesPageObjects.step4locDisPg2DrpDwn4);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step4DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step5locDisPg3)).toBeTruthy();
    const locDisableSc3Step5 = testData.getCellValue(SHEET_NAME, scenarioname, 'step5LocDisPg5');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step5locDisPg3, locDisableSc3Step5);
    await elementActions.click(locationServicesPageObjects.step5locDisPg3DrpDwn5);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step5DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step6locDisPg3)).toBeTruthy();
    const locDisableSc3Step6 = testData.getCellValue(SHEET_NAME, scenarioname, 'step6LocDisPg5');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step6locDisPg3, locDisableSc3Step6);
    await elementActions.click(locationServicesPageObjects.step6locDisPg3DrpDwn6);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step6DrpDwnImg)).toBeTruthy();
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step7locDisPg5)).toBeTruthy();
    const locDisableSc5Step7 = testData.getCellValue(SHEET_NAME, scenarioname, 'step7LocDisPg5');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.step7locDisPg5, locDisableSc5Step7);
    await elementActions.click(locationServicesPageObjects.step7locDisPg5DrpDwn7);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.step7DrpDwnImg)).toBeTruthy();
 
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.screengrabs)).toBeTruthy();
    const screenGrabs = testData.getCellValue(SHEET_NAME, scenarioname, 'screengrabsTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.screengrabs, screenGrabs);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.afterLocTxtDisPg2)).toBeTruthy();
    const afterLocationDisPg2Text = testData.getCellValue(SHEET_NAME, scenarioname, 'afterEnableTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.afterLocTxtDisPg2, afterLocationDisPg2Text);
 
    const continueBtnText = testData.getCellValue(SHEET_NAME, scenarioname, 'continueBtn');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.continueBtn, continueBtnText);
    await elementActions.click(locationServicesPageObjects.continueBtn);
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
 
  }
 
 
  async locationDisableLastScreenandClickOnTryAgain(filename: string, sheetname: string, scenarioname: string) {
    const SHEET_NAME = sheetname;
    const jsonFilePath = path.join(process.cwd(), 'data', filename);
    const testData = new JsonTestDataHandler(jsonFilePath);
    const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
    console.log(`User Data for TC02: ${JSON.stringify(userData1)}`);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
    const locDisableHdrPg2 = testData.getCellValue(SHEET_NAME, scenarioname, 'locationDisabledTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.locDisPg2Hdr, locDisableHdrPg2);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.lstPgHeadTxt)).toBeTruthy();
    const locDsblLstPgHeadTxt = testData.getCellValue(SHEET_NAME, scenarioname, 'lastPgHeadText');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.lstPgHeadTxt, locDsblLstPgHeadTxt);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.cstmrContactNumber)).toBeTruthy();
    const contactNumber = testData.getCellValue(SHEET_NAME, scenarioname, 'contactNumber');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.cstmrContactNumber, contactNumber);
 
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.cstmrContanctMail)).toBeTruthy();
    const contactMail = testData.getCellValue(SHEET_NAME, scenarioname, 'contactMail');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.cstmrContanctMail, contactMail);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.tryAgainBtn)).toBeTruthy();
    const tryAgain = testData.getCellValue(SHEET_NAME, scenarioname, 'tryAgain');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.tryAgainBtn, tryAgain);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.backToGrizzlyAemPage)).toBeTruthy();
    const backToGrizzly = testData.getCellValue(SHEET_NAME, scenarioname, 'backtoAemPg');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.backToGrizzlyAemPage, backToGrizzly);
 
    await elementActions.click(locationServicesPageObjects.tryAgainBtn);
 
  }
 
 
  async locationDisableLastScreenandClickOnBackToGrizzly(filename: string, sheetname: string, scenarioname: string) {
    const SHEET_NAME = sheetname;
    const jsonFilePath = path.join(process.cwd(), 'data', filename);
    const testData = new JsonTestDataHandler(jsonFilePath);
    const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
    console.log(`User Data for TC02: ${JSON.stringify(userData1)}`);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.locDisPg2Hdr)).toBeTruthy();
    const locDisableHdrPg2 = testData.getCellValue(SHEET_NAME, scenarioname, 'locationDisabledTxt');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.locDisPg2Hdr, locDisableHdrPg2);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.lstPgHeadTxt)).toBeTruthy();
    const locDsblLstPgHeadTxt = testData.getCellValue(SHEET_NAME, scenarioname, 'lastPgHeadText');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.lstPgHeadTxt, locDsblLstPgHeadTxt);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.cstmrContactNumber)).toBeTruthy();
    const contactNumber = testData.getCellValue(SHEET_NAME, scenarioname, 'contactNumber');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.cstmrContactNumber, contactNumber);
 
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.cstmrContanctMail)).toBeTruthy();
    const contactMail = testData.getCellValue(SHEET_NAME, scenarioname, 'contactMail');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.cstmrContanctMail, contactMail);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.tryAgainBtn)).toBeTruthy();
    const tryAgain = testData.getCellValue(SHEET_NAME, scenarioname, 'tryAgain');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.tryAgainBtn, tryAgain);
 
    expect(await elementActions.isDisplayed(locationServicesPageObjects.backToGrizzlyAemPage)).toBeTruthy();
    const backToGrizzly = testData.getCellValue(SHEET_NAME, scenarioname, 'backtoAemPg');
    await assertionHelper.mssgcomparision(locationServicesPageObjects.backToGrizzlyAemPage, backToGrizzly);
 
 
    const _currentWindowHandle = await browser.getWindowHandle();
 
 
 
    await elementActions.click(locationServicesPageObjects.backToGrizzlyAemPage);
 
    const contexts = await driver.getContexts();
    console.log('present context:', contexts);
    //browser.pause(3000);
    await browser.acceptAlert();
 
 
    const contexts1 = await driver.getContexts();
    console.log('present contexty:', contexts1);
 
    const newHandles = await browser.getWindowHandles();
    console.log('New Window Handles:', newHandles);
    const _handles = await browser.getWindowHandles();
    await browser.switchToWindow(newHandles[1]);
    await assertionHelper.assertElementDisplayed(HamburgerMenuPageObjects.brandIcon_AEMPage);
 
 
 
 
  }
  
}

export default new LocationServices();
