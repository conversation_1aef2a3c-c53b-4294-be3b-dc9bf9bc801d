import { browser } from '@wdio/globals';
import Page from './Page.ts';
import elementActions from '../support/actions/element.actions.ts';
import mobileActions from '../support/actions/mobile.actions.ts';
import logger from '../support/utils/logger.util.ts';
import loginPageObjects from '../pageObjects/Login.pageObjects.ts';
import aemCouponsPageObjects from '../pageObjects/AEMCoupons.pageObjects.ts';
import LoginPageObjects from '../pageObjects/Login.pageObjects.ts';
import AltHomePageObjects from '../pageObjects/AltHome.pageObjects.ts';
import locationServicePage from './locationService.page.ts';
import LocationServicesPageObjects from '../pageObjects/LocationServices.pageObjects.ts';
import { DBUtilities } from '../support/utils/dbUtilities.ts';
import { accountHasOffers, MobileOffersConfig } from '../support/utils/mobileOffersChecker.util.ts';


/**
 * sub page containing specific selectors and methods for a specific page
 */

class LoginPage extends Page {

  dbUtilities = new DBUtilities;

  async setLocation(lat: number, lon: number): Promise<void> {
    await mobileActions.setLocation(lat, lon);
    
  }

  async login(username: string, password: string, brand:string): Promise<void> {
    try {
      const url=await browser.getUrl();
      if(await url.includes('qa')){
      const userId = await this.getUserIdFromDB();
      if (userId) {
        username = userId;
      }
      console.log('userID :', userId);
      logger.info(`Attempting login for user: ${userId}`);
    }
    else{
 
    }
      logger.info(`Attempting login for user: ${username}`);
      await expect(loginPageObjects.usernameInput).toBeDisplayed();
      await expect(loginPageObjects.passwordInput).toBeDisplayed();
      await expect(loginPageObjects.loginButton).toBeDisplayed();
      await elementActions.setValue(loginPageObjects.usernameInput, username);
      await elementActions.setValue(loginPageObjects.passwordInput, password);
      if (driver.isIOS) {
        await (await loginPageObjects.loginButton).click();
      } else {
        await browser.keys('Enter');
      }
      // await (await loginPageObjects.postLoginLogo).waitForExist({
      //   timeout: 20000,
      //   timeoutMsg: "Home page has not loaded after 20s",
      // });
      // expect(await loginPageObjects.postLoginLogo).toBeExisting();
    } catch (error) {
      logger.error('Login failed', { error });
      throw error;
    }
  }

  async clickLogout(): Promise<void> {
    await elementActions.click(loginPageObjects.logoutMyProfile);
  }

  async checkRememberMe(): Promise<void> {
    await elementActions.click(loginPageObjects.rememberMeCheckbox);
  }

  async getLoginErrorMessage() {
    let text;
    await browser.waitUntil(
      async () => {
        text = await loginPageObjects.loginErrorMsg.getText();
        return text.length > 0;
      },
      { timeout: 10000, timeoutMsg: 'Expected text to be present after 5s' },
    );
    await elementActions.highlightElement(loginPageObjects.loginErrorMsg);
    return text;
  }

  


  async getRememberedUsername() {
    return await loginPageObjects.usernameInput.getValue();
  }

  async waitForSuccessfulLogin() {
    try {
      await browser.waitUntil(
        async () => {
          const sgwVisible = await aemCouponsPageObjects.sgwWarning.isDisplayed().catch(() => false);
          if (sgwVisible) { await elementActions.highlightElement(aemCouponsPageObjects.sgwWarning); }
          const couponBtnVisible = await aemCouponsPageObjects.mobileCouponButton.isDisplayed().catch(() => false);
          if (couponBtnVisible) { await elementActions.highlightElement(aemCouponsPageObjects.mobileCouponButton); }
          return sgwVisible || couponBtnVisible;
        },
        {
          timeout: 15000,
          timeoutMsg: 'Login failed: Authentication indicators not visible after 15s',
          interval: 500,
        },
      );

      logger.info('Login successful: Authentication indicators detected');
    } catch (error) {
      logger.error('Login verification failed', { error });
      await browser.saveScreenshot('./screenshots/login_failure.png');
      throw new Error('Login verification failed: ' + (error instanceof Error ? error.message : String(error)));
    }
  }
  /**
       * Comprehensive check if user is logged in
       * @returns {Promise<boolean>} True if user is logged in
       */
  async isUserLoggedIn() {
    try {
      // Check URL first (fastest check)
      const currentUrl = await browser.getUrl();
      const urlIndicatesLoggedIn = currentUrl.includes('/coupons') || currentUrl.includes('/secure');

      if (urlIndicatesLoggedIn) {
        // Verify at least one authenticated element exists for confirmation
        return await this.hasAnyAuthenticatedElement();
      }

      // If URL doesn't indicate logged in state, check elements directly
      return await this.hasAnyAuthenticatedElement();
    } catch (error) {
      console.error('Error checking login status:', error);
      return false;
    }
  }



  
  /**
  * Helper method to check for any elements that indicate authentication
  * @returns {Promise<boolean>} True if any authenticated elements exist
  */
  private async hasAnyAuthenticatedElement() {
    const results = await Promise.all([
      aemCouponsPageObjects.sgwWarning.isExisting().catch(() => false),
      aemCouponsPageObjects.mobileCouponButton.isExisting().catch(() => false),
    ]);

    // Return true if ANY element exists
    return results.some(result => result === true);
  }

  async openHamburgerMenu(): Promise<void> {
    await elementActions.click(loginPageObjects.hmBurgerMenu);
    await elementActions.click(loginPageObjects.myProfileHmMenu);
  }


  async loginwithRememberMeChckBox(username: string, password: string): Promise<void> {
    try {
      logger.info(`Attempting login for user: ${username}`);
      await expect(loginPageObjects.usernameInput).toBeDisplayed();
      await expect(loginPageObjects.passwordInput).toBeDisplayed();
      await expect(loginPageObjects.loginButton).toBeDisplayed();
      await elementActions.setValue(loginPageObjects.usernameInput, username);
      await elementActions.setValue(loginPageObjects.passwordInput, password);
      await elementActions.click(loginPageObjects.rememberMeCheckbox);
      if (driver.isIOS) {
        await (await loginPageObjects.loginButton).click();
      } else {
        await browser.keys('Enter');
      }
      
    } catch (error) {
      logger.error('Login failed', { error });
      throw error;
    }
  }

  async logintopwawithvaliduseridandpassword(username:string,password: string,brand:string):Promise<void>
  {
    try{
      if(driver.isIOS ){
      const contexts = await driver.getContexts();
      await driver.switchContext('NATIVE_APP');
await driver.waitUntil(async () => {
    const currentContexts = await driver.getContexts();
    return currentContexts.length > 1;
}, { timeout: 10000 });

const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));

if (webviewContext) {
    await driver.switchContext(webviewContext);
} else {
    throw new Error('WEBVIEW context not found');
}
      
    const contextHandles = await browser.getContexts();
    
   const secondWindow = contextHandles[1];
    await browser.switchContext(secondWindow);
    await expect(loginPageObjects.usernameInput).toBeDisplayed();
      await expect(loginPageObjects.passwordInput).toBeDisplayed();
      await expect(loginPageObjects.loginButton).toBeDisplayed();
      await elementActions.setValue(loginPageObjects.usernameInput, username);
      await elementActions.setValue(loginPageObjects.passwordInput, password);
      if (driver.isIOS) {
        await (await loginPageObjects.loginButton).click();
      } else {
        await browser.keys('Enter');
      }
      }
    }
    catch(error)
    {
      logger.error('Login failed', { error });
      throw error;
    }
  }

  async installPWAOnAndroid() {
    // Click on Chrome menu (3 dots)
    await driver.switchContext('NATIVE_APP');
    await loginPageObjects.chromeMenuBtn.click();

    // Click "Add to Home screen" option
    await loginPageObjects.chromeAddToHomeScreenBtn.click();

    // Click first Install button
    await loginPageObjects.chromeInstallBtn.click();

    // Handle install popup and click final Install button
    await loginPageObjects.chromeConfirmInstallBtn.click();

    // try {
    //     await this.chromePopUpAddToHomeScreenLayover.waitForExist({ timeout: 5000 });
    //     await this.chromPopUpAddToHomeScreenBtn.click();
    //   } catch (error) {
    //     if (error instanceof Error) {
    //       console.log(`Ignoring error: ${error.message}`);
    //     }
    //   }
}

async loginPWAApp(username: string, password: string): Promise<void> {
  try {
    logger.info(`Attempting login for user: ${username}`);
    
    if(await elementActions.isElementPresent(loginPageObjects.rememberedcheckbox))
    {
       await elementActions.click(loginPageObjects.rememberedcheckbox);
    }
    
    await elementActions.setValue(await loginPageObjects.usernameInput, username);
    await elementActions.setValue(await loginPageObjects.passwordInput, password);
    await browser.execute('arguments[0].click();', await loginPageObjects.loginButton);
    if(driver.isAndroid) {
    await AltHomePageObjects.welcomeBackMsg.waitForExist({
      timeout: 30000,
      timeoutMsg: 'AltHome page has not loaded after 20s',
    });
    await expect(await AltHomePageObjects.welcomeBackMsg).toBeExisting();
  } else {
    await LocationServicesPageObjects.understoodBtn.waitForExist({
      timeout: 20000,
      timeoutMsg: 'AltHome page has not loaded after 20s',
    });
    await expect(await LocationServicesPageObjects.understoodBtn).toBeExisting();
  }
  } catch (error) {
    logger.error('Login to PWA App failed', { error });
    throw error;
  }
}
async launchAndroidPWA(applicationName: string) {
    try {
        // Find the WebAPK package
        const pkg = await this.findWebAPKPackageName();

        // Wait for initial stability
        await browser.pause(5000);

        // Get the main activity
        const activityInfo = await driver.executeScript('mobile: shell', [
            {
                command: `dumpsys package ${pkg} | grep -A 1 "android.intent.action.MAIN:"`,
            },
        ]);

        console.log('Activity info:', activityInfo);

        // Try to extract activity name or use default
        const lines = activityInfo.split('\n');
        let launchActivity;
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].trim().startsWith('android.intent.action.MAIN:')) {
                const nextLine = lines[i + 1];
                launchActivity = nextLine.trim().split(' ')[1].split('/').pop();
                break;
            }
        }
        console.log('Launching activity:', launchActivity);
        // Get app label info
        const appLabelInfo = await driver.executeScript('mobile: shell', [
            {
                command: `dumpsys package ${pkg} | grep -A 1 "Authority:"`,
            },
        ]);

        let appLabel: string;

        /*await appLabelInfo
            .trim()
            .split("\n")
            .forEach((line) => {
                if (line.startsWith("Authority:")) {
                    const authority = line.split(":")[1].trim().split('"')[1];
                    appLabel = authority.split(".")[1];
                }
            });*/

       // console.log(`App Label for ${pkg}:`, appLabel);

        // Check if the application name matches
        /*if (!await appLabel.trim().toLowerCase().includes(applicationName.toLowerCase())) {
            throw new Error(`${applicationName} PWA app not found in installed packages`);
        }*/

        await console.log(`Found matching package: ${pkg}`);

        // Launch the app
        await driver.executeScript('mobile: shell', [{
            command: `am start -n ${pkg}/${launchActivity}`,
        }]);

        await browser.pause(5000);

    } catch (error) {
        await console.error('Error launching PWA:', error);
        throw error;
    }
}
private async findWebAPKPackageName(
    timeoutMs: number = 50000,
): Promise<string> {
    const startTime = Date.now();

    while (Date.now() - startTime < timeoutMs) {
        try {
            // Use dumpsys to get package information and grep for WebAPK
            const result = await driver.executeScript('mobile: shell', [
                {
                    command:
                        'dumpsys package | grep -i "Package \\[org.chromium.webapk"',
                },
            ]);

            console.log('WebAPK packages found:', result);

            // Extract full package name from the result
            const packageMatch = await result.match(
                /Package \[(org\.chromium\.webapk\.[^\]]+)\]/,
            );
            if (packageMatch && packageMatch[1]) {
                const fullPackageName = packageMatch[1];
                console.log('Found WebAPK package:', fullPackageName);
                return fullPackageName;
            }
        } catch (error) {
            console.error('Error finding WebAPK package:', error);
        }

        await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait for 1 second
    }

    throw new Error(
        `No WebAPK package found within ${timeoutMs / 1000} seconds`,
    );
}

async installPWAOniOS() {
    // Click share button
    driver.switchContext('NATIVE_APP');
    await loginPageObjects.iosShareBtn.click();
    await browser.pause(1000);
    await driver.performActions([
        {
            type: 'pointer',
            id: 'finger1',
            parameters: { pointerType: 'touch' },
            actions: [
                { type: 'pointerMove', duration: 0, x: 207, y: 556 },
                { type: 'pointerDown', button: 0 },
                { type: 'pointerMove', duration: 1000, x: 226, y: 238 },
                { type: 'pointerUp', button: 0 },
            ],
        },
    ]);
    // Click "Add to Home Screen" option
    await loginPageObjects.iosAddToHomeBtn.click();

    // Click Add button in top right
    await loginPageObjects.iosAddBtn.click();
    // Wait for installation to complete
    await browser.pause(3000);
}

async launchPWA(appName: string) {
    // Navigate to home screen
    await driver.switchContext('NATIVE_APP');
    if (await driver.isAndroid) {
        await this.launchAndroidPWA(appName);
        await driver.switchContext('CHROMIUM');
    } else if (await driver.isIOS) {
        await this.launchiOSPWAApp(appName);
        const contexts = await driver.getContexts();
        await driver.waitUntil(async () => {
            const contexts = await driver.getContexts();
            return contexts.length > 1;
        }, { timeout: 10000 });
        
        const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
        //await driver.switchContext(webviewContext.toString());
        if (webviewContext) {
          await driver.switchContext(webviewContext.toString());
      }
        
    }

    // Wait for PWA to launch
    await browser.pause(5000);

    if (await driver.isAndroid) {
        // Verify PWA is launched by checking for PWA-specific element
        // Wait until there are at least two window handles
        await driver.waitUntil(async () => {
            const windowHandles = await driver.getWindowHandles();
            return windowHandles.length > 1;
        }, { timeout: 10000 }); // Wait for up to 10 seconds

        // Get the window handles again to ensure we have the latest values
        const windowHandles = await driver.getWindowHandles();

        // Switch to the new window handle (the second one in the array)
        await driver.switchToWindow(windowHandles[1].toString());
    }

    // Wait for login button
    await loginPageObjects.loginButton.waitForDisplayed({ timeout: 60000 });
}

// Helper method to check if we're on the last page of apps
private async isLastPage(platform: string): Promise<boolean> {
    // Implementation depends on platform-specific indicators
    if (platform === 'Android') {
        // Check for last page indicator (might vary by device)
        const lastPageIndicator = await $('//*[@resource-id="last-page-indicator"]');
        return await lastPageIndicator.isDisplayed();
    } else if (platform === 'iOS') {
        // Check for last page dot indicator
        const pageIndicators = await $$('XCUIElementTypePageIndicator');
        const lastDot = await pageIndicators[await pageIndicators.length - 1];
        return await lastDot.getAttribute('selected') === 'true';
    }
    return false;
}

private async scrollToFind(selector: string, maxScrolls: number = 5): Promise<boolean> {
    for (let i = 0; i < maxScrolls; i++) {
        // Check if element is already visible
        const element = $(selector);
        if (await element.isExisting()) {
            return true;
        }
        // Perform scroll based on platform
        if (await driver.isAndroid) {
            await browser.touchAction([
                { action: 'press', x: 500, y: 1000 },
                { action: 'moveTo', x: 500, y: 200 },
                'release',
            ]);
        } else if (await driver.isIOS) {
            await browser.execute('mobile: scroll', {
                direction: 'up',
                distance: 100,
            });
        }

        // Small pause to let scroll complete
        await browser.pause(1000);
    }
    return false;
}

async launchiOSPWAApp(appName: string) {
    // Go to home screen
    //await browser.execute('mobile: pressButton', { name: 'home' });            
    // Define selector for PWA icon
    const iOSPWAIcon = `-ios class chain:**/XCUIElementTypeIcon[\`label == "${appName}"\`]`;

    // Scroll to find the PWA icon
    const found = await this.scrollToFind(iOSPWAIcon);
    if (!found) {
        throw new Error('Could not find PWA icon after scrolling');
    }

    // Perform a tap action on the element using execute
    const icons = (await $$(iOSPWAIcon));
    for (const icon of icons) {
        // Get element's location and viewport information
        const isVisible = await driver.isElementDisplayed(await icon.elementId);
        if (isVisible) {
            await icon.click();
            break;
        }
    }

}

async installPWAApp() {
    if (await driver.isAndroid) {
        await this.installPWAOnAndroid();
    } else if (await driver.isIOS) {
        await this.installPWAOniOS();
    }
  }

  async getUserIdFromDB(config?: MobileOffersConfig): Promise<string | null> {
    try {
      await this.dbUtilities.connectDB(
        process.env.DB_USERNAME || '',
        process.env.DB_PASSWORD || '',
        process.env.DB_SERVER || '',
      );
 
      const sqlQuery = `
            SELECT TOP (1) a.ACCTNO, a.USERID
            FROM [RAI_APP].[dbo].[Consumer_Credentials] a
            JOIN [RAI_BASE].[dbo].[CONSUMERS_ACCTNO] b ON a.ACCTNO = b.ACCTNO
            JOIN [RAI_BASE].[dbo].[CONSUMERS_ADDRESS] c ON a.ACCTNO = c.ACCTNO
            WHERE a.PASSWORD = '4tJ22wPRKQsXr8hazDXdVBzVJFtlEnce7zBfz2FVuYF/2nPdSnETbB82b/Sg0/Ce0OghjPdBu4NQmSjKEVXcmg=='
              AND a.WEBACCESS = 'U'
              AND a.LOGIN_RESET = 'N'
              AND a.CREATE_DATE > '2024-12-07 10:23:00'
              AND c.STATE NOT IN ('MA','NJ','NY')
            ORDER BY NEWID()
          `;
 
      const maxAttempts = 5;
      let attempt = 0;
 
      while (attempt < maxAttempts) {
        attempt++;
        const result = await this.dbUtilities.query(sqlQuery);
 
        if (result?.recordset?.length > 0) {
          const { ACCTNO, USERID } = result.recordset[0];
          console.log(`Attempt ${attempt}: Data retrieved from the DB:`, [ACCTNO, USERID]);
 
          // Check if account has offers
          const hasOffers = await accountHasOffers(USERID, ACCTNO, config);
 
          if (hasOffers) {
            console.log(`Account ${ACCTNO} has offers. Using this account.`);
            return USERID; // Return only the USERID
          } else {
            console.log(`Account ${ACCTNO} has no offers. Trying another account...`);
            // Continue to next iteration to try another account
          }
        } else {
          console.log(`No accounts found on attempt ${attempt}`);
        }
      }
 
      console.log(`Failed to find account with offers after ${maxAttempts} attempts`);
      return null;
    } catch (error) {
      console.error('Error fetching data from DB:', error);
      return null;
    }
  }

}

export default new LoginPage();
