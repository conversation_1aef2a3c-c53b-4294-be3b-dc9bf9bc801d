import { $ } from '@wdio/globals';
import Page from '../pages/Page.ts';

class CouponHomePageObjects extends Page {
    //validateUserOnActiveCouponPage: any;

    get brandLogo() { return $('img[alt="Navbar Logo"]'); }
    get allCoupons() { return $$('div[data-testid="couponsListDescription"]'); }
    get allCpns() { return $('//div[@class="_coupons-list-container_av0im_1"]'); }
    get couponsPrice() { return $('//p[@class="_coupons-list-price_1yo3t_33"]'); }
    get couponsMessage() { return $('//p[@class="_coupons-list-description-text_1yo3t_1"]'); }
    get couponDescriptionStyle() { return $('//p[@class="_coupons-list-description-style_1yo3t_1"]'); }
    get couponsTnkuMesge() { return $('//div[@class="_coupons-list-header-welcome-text_1yo3t_10 _coupon-header-thankyou_1yo3t_264"]'); }
    get couponsExpDt() { return $('//div[@class="_coupons-list-header-expire-text_1yo3t_38"]'); }
    get couponExpDtList() { return $('//div[text()="Expires "]');}
    get welcmFstNameTxt() { return $('//*[@id="welcomeContainer"]'); }
    get hmbrgrMenu() { return $('//*[@alt="Ham Burger Menu"]'); }
    get btnSortOffers() { return $('//img[@alt="Sort By Value"] | //img[@alt="Sort By Expiration"]'); }
    get lblSortBy() { return $('//p[contains(text(),"Sort By:")]'); }
    get btn_ChooseStore() { return $('//div[text()="CHOOSE A STORE"]'); }
    get personalizedCoupon() { return $('(//div[@data-testid="couponsListDescription"])[2] | (//div[@data-testid="couponsListDescription"])[1] | (//div[contains(@id,"coupon-list-choice-container")])[1]'); }
    get weeklyCpn() { return $('(//div[text()="WEEKLY"])[1]');}
    get couponCount() { return $('//span[@data-testid="coupons-count-value"]');}                           
    get couponsImg() { return $('//img[@alt="coupons image"]');}
    get offerVlidTxt() { return $('//*[contains(text(),"THIS COUPON OFFER IS VALID FOR")]');}
    get couponsProgressBar() { return $('//div[@class="_coupons-list-progressBar_1yo3t_149"]');}
    get daysLeft() { return $('//div[@class="_coupons-list-offerDetails-days_1yo3t_132"]');}
    get birthDayCpn() { return $('(//div[contains(text(),"HAPPY BIRTHDAY")])[1]');}
    get welComeCpn() { return $('(//*[contains(text(),"WELCOME")])[1]');}
    get thankYouCpn() { return $('(//*[contains(text(),"THANK")])[1]');}

    get readMore() { return $('//p[text()="Read more"]');}
    get readLess() { return $('//p[text()="Read less"]');}
    get couponTerms(){ return $('//div[contains(@class,"_coupon-terms-text_")]');}
    get sgwWarning() { return $('//*[contains(@class,"_fixed-warning-c")]');}
    get camelSGWWarning() { return $('img[src*="warningcontent/images/q3/248.png"]');}
    get either_Coupon1() { return $('(//div[contains(@id,"coupon-list-choice-container")])[1]');}
    get either_Coupon2() { return $('(//div[contains(@id,"coupon-list-choice-container")])[2]');}
    get either_OrIcon() { return $('//div[@class="_coupon-list-choice-or-image_fxvke_10"]');}
    get coupon_Arrow1() { return $('//img[@alt="EitherOrRight"]');}
    get coupon_Arrow2() { return $('//img[@alt="EitherOrLeft"]');}

    get couponAllTerms() { return $('//div[@data-testid="couponAllTerms"]');}
    get termRemptnTxt() { return $('//p[contains(@class,"_coupon-offers-redemtion-text")]');}
    get termsHeading() { return $('//p[contains(@class,"_coupon-terms-text-heading")]');}
    get voidText() { return $('//p[contains(@class,"_coupon-void-text")]');}

    get fixedWrng1() { return $('//*[contains(text(),"UNDERAGE SALE PROHIBITED")]');}
 
    get fixedWrng2() { return $('//*[contains(text(),"NICOTINE PRODUCTS")]');}
 
  get sortByTxt() { return $('//*[contains(text(),"Sort By:")]');}
    getcouponsPrice(i: number) { return $(`(//p[contains(@class,"_coupons-list-price")])[${i}]`);}
    getcouponsImage(i: number) { return $(`(//img[@alt="coupons image"])[${i}]`);}
    getcouponsExpDate(i: number) { return $(`(//div[text()="Expires "])[${i}]`);}
    getcouponsOfrVldTxt(i: number) { return $(`(//div[contains(@class,"_coupons-list-offerDetails-validFor")])[${i}]`);}
    getcouponsOf1CpnTxt(i: number) { return $(`(//*[contains(text(),"OFF ")])[${i}]`);}
    getcouponsGdStlTxt(i: number) { return $(`(//*[contains(text(),"GOOD ON ANY STYLE")])[${i}]`);}
    getcouponsPrgrsBar(i: number) { return $(`(//div[contains(@class,"_coupons-list-progressBar")])[${i}]`);}
    getcouponsDaysLeft(i: number) { return $(`(//div[contains(@class,"_coupons-list-offerDetails-days")])[${i}]`);}
    getcouponsChooseStore(i: number) { return $(`(//div[text()="CHOOSE A STORE"])[${i}]`);}

}


export default new CouponHomePageObjects();