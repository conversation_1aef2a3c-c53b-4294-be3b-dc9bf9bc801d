
class HamburgerMenuPageObject {
    
     get hmbrgrMenu() { return $('//img[@alt="Ham Burger Menu"]'); }
     get grizzly_couponshmlink() { return $('//p[text()="Coupons"]'); }
     get grizzly_brandsitehmlink() { return $('//p[text()="GRIZZLYNICOTINEPOUCHES.COM"]'); }
     get grizzly_logouthmlink() { return $('//p[text()="Logout"]'); }
     get brandIcon() { return $('//img[@alt="Navbar Logo"]');}
     get grizzly_SGW_wrnig() { return $('//p[@class="_fixed-warning-content_lvvbq_41 "]');}
     get closeHamMenu() { return $('//img[@alt="Close Ham Menu"]');}
     get grizzly_AEMPage_SGW_wrnig() { return $('//*[@class="cmp-surgeon-general-warning__warning-text"]');} 
     get brandIcon_AEMPage() { return $('(//img[@alt="GRIZZLYMO"])[2]');}

     get aEMhomeButton() { return $('//*[text()="Home"]');}
     get aEMlogoutButton() { return $('//*[text()="Logout"]');}
    }

export default new HamburgerMenuPageObject();
