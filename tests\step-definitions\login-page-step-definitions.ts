import { Given, When, Then } from '@wdio/cucumber-framework';
import logger from '../support/utils/logger.util.ts';
import { expect as expectChai } from 'chai';
import mobileActions from '../../tests/support/actions/mobile.actions.ts';
import loginPage from '../pages/login.page.ts';
import AltHomePage from '../pages/AltHome.page.ts';
import locationServicePage from '../pages/locationService.page.ts';

// Page URL navigation
Given(/^I (?:am|\'m) on the login page for (.*) with (?:login |URL )(.*)$/, async function (brand: string, url: string) {
    // [Given] Sets up the initial state of the system.
    await logger.info(`Step: Navigating to login page of ${brand}`);
    await loginPage.open(url);

    await logger.info(`Navigated to ${brand} login page: ${url}`);
});

// Location setting
Given(/^I set the device location to (.+) and (.+)$/, async function (latitude: string, longitude: string) {
    // [Given] Sets up the initial state of the system.
    await logger.info('Step: Set the device location');
    const lat = parseFloat(latitude);
    const lon = parseFloat(longitude);
    await loginPage.setLocation(lat, lon);
    await logger.info(`Set device location to: Latitude: ${lat}, Longitude: ${lon}`);
});

// Login attempts
When(/^I (?:login|attempt to login) with (?:valid |invalid )?(?:user id|username|credentials username) (.*) and password (.*)(?: for (.*))?$/, async function (invalidusername: string, password: string, brand: string) {
    await logger.info(`Step: Attempted login with username: ${invalidusername} for ${brand || 'unknown brand'}`);
    await loginPage.login(invalidusername.trim(), password.trim(),brand);
    await logger.info(`Attempted login with username: ${invalidusername} for ${brand || 'unknown brand'}`);
});

When(/^I (?:login|attempt to login) Remember me Checkbox with (?:valid |invalid )?(?:user id|username|credentials username) (.*) and password (.*)(?: for (.*))?$/, async function (username: string, password: string, brand: string) {
    await logger.info(`Step: Attempted login with rememberme checkbox with username: ${username} for ${brand || 'unknown brand'}`);
    await loginPage.loginwithRememberMeChckBox(username.trim(), password.trim());
    await logger.info(`Attempted login with rememberme checkbox with username: ${username} for ${brand || 'unknown brand'}`);
});


When(/^I check the remember me checkbox$/, async function () {
    console.log('Selecting Remember Me checkbox');
    await loginPage.checkRememberMe();
    await logger.info('Selected Remember Me checkbox');
});

When(/^I login with 128 characters? (?:valid )?(?:user id|username) (.*) and password (.*)(?: for (.*))?$/, async function (username: string, password: string, brand: string) {
    expectChai(username.length).to.be.at.most(128, 'Username exceeds 128 characters');
    await loginPage.login(username.trim(), password.trim(),brand);
    await logger.info(`Logged in with 128-character username for ${brand || 'unknown brand'}`);
});

// Navigation actions
Then(/^I navigate to (?:the )?Grizzly Hamburger Menu$/, async function () {
    await loginPage.openHamburgerMenu();
    await browser.pause(1000);
    console.log('Navigated to Hamburger Menu');
    await logger.info('Navigated to Hamburger Menu');
});

Then(/^I click on logout(?: option)?(?: in Grizzly aem site)?$/, async function () {
    //await HamburgerMenuPage.clickLogout();
    await loginPage.clickLogout();
    await browser.pause(2000); // Wait for logout to complete
    console.log('Clicked on logout option');
    await logger.info('Clicked on logout option');
});

// Verification steps
Then(/^I should see an error message indicating invalid login(?: credentials)?$/, async function () {
    const errorMessage = await loginPage.getLoginErrorMessage();
    expectChai(errorMessage).to.not.be.empty;
    expectChai(errorMessage).to.include('USERNAME OR PASSWORD INCORRECT.');
    console.log(`Error message displayed: ${errorMessage}`);
    await logger.info(`Error message displayed: ${errorMessage}`);
});




Then(/^I should see my (.*) persisted in the login field$/, async function (expectedUsername: string) {
    const rememberedUsername = await loginPage.getRememberedUsername();
    expectChai(rememberedUsername).to.not.be.empty;
    console.log(`Username persisted in field: ${rememberedUsername}`);
    await logger.info(`Username persisted in field: ${rememberedUsername}`);
});

Then(/^I should be successfully logged in$/, async function () {
    await loginPage.waitForSuccessfulLogin();
    const isLoggedIn = await loginPage.isUserLoggedIn();
    expectChai(isLoggedIn).to.be.true;
    console.log('User successfully logged in');
    await logger.info('User successfully logged in');
});

// Network condition tests
When(/^I (?:turn ON|enable) the? Airplane mode$/, async function () {
    await mobileActions.enableAirplaneMode();
    console.log('Airplane mode enabled');
    await logger.info('Airplane mode enabled');
});

Then(/^I should see an appropriate connectivity error message$/, async function () {
    // TODO: Implement this step
    // const errorMessage = await loginPage.getNetworkErrorMessage();
    // expectChai(errorMessage).to.include('connection');
    // console.log(`Network error message displayed: ${errorMessage}`);
    await logger.info('Checking for network connectivity error message');
});


Then(/^I login to PWA with valid user id (.*) and password (.*) for the brand (.*)$/, async function (username:string, password:string, brand:string) {
  
    await logger.info('Login to PWA with valid user id and password for the brand')
    await loginPage.loginPWAApp(username, password);
    if(await driver.isIOS ) {
        await locationServicePage.navigateToActiveCouponPage();
    }    
    //await AltHomePage.validateUserIsOnAltHomePage();
    
  
});
