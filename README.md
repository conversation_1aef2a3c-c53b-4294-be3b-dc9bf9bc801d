# RJR WebdriverIO-Cucumber Test Automation Framework

This is a WebdriverIO-based test automation framework that leverages TypeScript and Cucumber for behavior-driven testing. It is designed for testing web, mobile, and hybrid applications on Sauce Labs.

## 📖 Table of Contents

- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Project Structure](#project-structure)
- [Running Tests](#running-tests)
- [Writing Tests](#writing-tests)
- [Configuration](#configuration)
- [Reports](#reports)
- [Troubleshooting](#troubleshooting)

## 📌 Prerequisites

Ensure you have the following installed:

- **Node.js (>= 16.x)**
- **npm (>= 8.x)**
- **WebdriverIO (>= 8.x)**
- **TypeScript**
- **Cucumber.js**
- **Allure Reporting Plugin**
- **Sauce Labs Credentials** (if running tests on Sauce Labs)

## 🔧 Installation

Clone the repository and install dependencies:

```sh
git clone <repo-url>
cd WDIO-CUCUMBER
npm install
```

## 📂 Project Structure

```
WDIO-CUCUMBER
│── node_modules/                # Dependencies installed via npm
│── reports/                     # Allure & other test reports
│── tests/                        # Main test folder
│   ├── configs/                  # WebdriverIO configuration files
│   │   ├── wdio.saucelabs.desktop.conf.ts
│   │   ├── wdio.saucelabs.mobile-native.conf.ts
│   │   ├── wdio.saucelabs.mobile-PWA.conf.ts
│   │   ├── wdio.saucelabs.mobile-SPA.conf.ts
│   │   ├── wdio.saucelabs.mobile.conf.ts
│   │   ├── wdio.saucelabs.shared.conf.ts
│   │   ├── wdio.shared.conf.ts
│   ├── features/                 # Cucumber feature files
│   │   ├── camel-native.feature
│   │   ├── camel-pwa.feature
│   │   ├── coupon-home.feature
│   │   ├── login.feature
│   ├── helpers/                   # Utility helper functions
│   ├── pages/                     # Page Object Model (POM) files
│   │   ├── native-app/
│   │   │   ├── camelHome.page.ts
│   │   │   ├── couponHome.page.ts
│   │   │   ├── locationService.page.ts
│   │   ├── login.page.ts
│   │   ├── page.ts
│   │   ├── pwa-alt-home.page.ts
│   ├── step-definitions/          # Step definitions for Cucumber
│   │   ├── camel-native-step-definitions.ts
│   │   ├── camel-pwa-step-definition.ts
│   │   ├── coupon-home-step-definitions.ts
│   │   ├── login-page-step-definitions.ts
│   ├── resources/                 # Additional test data & resources
│   ├── support/                   # Support utilities
│   │   ├── actions/
│   │   ├── constants/
│   │   ├── types/
│   │   ├── utils/
│── .env                           # Environment variables
│── .gitignore                     # Files to ignore in git
│── allure-categories.json         # Custom categories for Allure reports
│── custom-reporter.ts             # Custom reporting logic
│── package.json                   # Project dependencies
│── package-lock.json               # Lock file for dependencies
│── tsconfig.json                   # TypeScript configuration
│── wdio.conf.ts                    # Main WebdriverIO configuration
│── README.md                      # Project documentation
```

## 🚀 Running Tests

Run all tests:

```sh
npm test
```

Run specific tests using a tag:

```sh
npm run test -- --cucumberOpts.tagExpression="@smoke"
```

Run tests on Sauce Labs:

```sh
npm run test:sauce
```

## ✍️ Writing Tests

- Feature files (`.feature`) are located in the `tests/features/` folder.
- Step definitions are inside `tests/step-definitions/`.
- Page objects are defined in `tests/pages/`.
- Use Cucumber tags (`@smoke`, `@regression`) for test categorization.

## ⚙️ Configuration

WebdriverIO configurations are available in the `tests/configs/` folder. Update the necessary `wdio.saucelabs.*.conf.ts` files to match your requirements.

To run tests on Sauce Labs, update the `.env` file with your Sauce Labs credentials:

```sh
SAUCE_USERNAME=your-username
SAUCE_ACCESS_KEY=your-access-key
```

## 📊 Reports

Generate Allure reports:

```sh
npm run report
```

Serve Allure reports:

```sh
npm run allure:open
```

## 🔧 Troubleshooting

- **Tests failing due to timeouts?** Increase timeout values in `wdio.conf.ts`.
- **Element not found?** Verify selectors in the Page Object.
- **Sauce Labs connection issues?** Ensure credentials in `.env` are correct.

## 🛠️ Key Features

- **WebdriverIO with TypeScript**
- **Cucumber for BDD-style testing**
- **Page Object Model (POM) structure**
- **Supports Desktop, Mobile (Native, PWA, SPA) Testing**
- **Sauce Labs Integration**
- **Allure Reporting for test results**
- **Custom Reporting (`custom-reporter.ts`)**
- **Environment-based configurations (`.env` support)**

---

## 📞 Need Help?

For any questions, open an issue in the repository or reach out to the contributors.

---

Happy Testing 🚀!
