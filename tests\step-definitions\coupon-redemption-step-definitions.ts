import { When, Then } from '@wdio/cucumber-framework';
import logger from '../support/utils/logger.util.ts';
import couponRedemptionPage from '../pages/couponRedemption.page.ts';
import StoreDetailsPageObjects from '../pageObjects/StoreDetails.pageObjects.ts';


When(/^I select a coupon and click on choose a store button$/, async function () {
    await couponRedemptionPage.navigateToStoreListPage();
    await expect(await StoreDetailsPageObjects.txtZipCodeSearch).toBeDisplayed();
    console.log('User navigates from Coupon page to Store List page by clicking on choose a store button in coupon');
    
    
});

Then(/^I select a store from store list page$/, async function () {
    await couponRedemptionPage.navigateToRedeemNowPage();
    console.log('The user will select a store from store list page')
});

When(/^I click on the Redeem Now button$/, async function () {
    await couponRedemptionPage.navigateToActiveCouponPage();
    console.log('The user clicks on redeem now button')
});

Then(/^I click on the I'm Done button$/, async function () {
    await couponRedemptionPage.navigateToSurveyOptions();
    console.log('The user clicks on I am done button');
});

Then(/^I Validate the Grizzly Content Page$/, async function () {
    await couponRedemptionPage.validateGrizzlyContentPage();
    console.log('The user validate the Grizzly Content Page');
});

Then(/^I Validate the (.*) Content Page$/, async function (brand:string) {
    await couponRedemptionPage.validateGrizzlyContentPage();
    console.log('The user validates the content page for the brand');
});

When(/^I mark the store as favorite$/, async function () {
    await couponRedemptionPage.markStoreasFavourite();
    console.log('Click favourite Store Checkbox');
    await logger.info('Click favourite Store Checkbox');
});

Then(/^I navigate to Store List and validate the favoritestore$/, async function () {
    await couponRedemptionPage.validateFavouriteStoreStoreListPage();
    console.log('Validate favourite Store in Redeem Now');
    await logger.info('Validate favourite Store in Redeem Now');
});