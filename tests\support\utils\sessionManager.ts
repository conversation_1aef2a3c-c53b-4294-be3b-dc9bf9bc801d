import { logger } from '../helpers/logger.ts';

/**
 * Session Manager utility for handling WebDriver session timeouts and recovery
 */
class SessionManager {
    private sessionStartTime: number = 0;
    private readonly SESSION_WARNING_THRESHOLD = 60000; // 60 seconds
    private readonly SESSION_TIMEOUT_THRESHOLD = 80000; // 80 seconds (before 90s Sauce Labs timeout)

    /**
     * Marks the start of a new session or operation
     */
    public markSessionStart(): void {
        this.sessionStartTime = Date.now();
        logger.info('Session operation started');
    }

    /**
     * Checks if the session is approaching timeout
     */
    public isSessionApproachingTimeout(): boolean {
        const elapsed = Date.now() - this.sessionStartTime;
        return elapsed > this.SESSION_WARNING_THRESHOLD;
    }

    /**
     * Checks if the session has exceeded the safe timeout threshold
     */
    public hasSessionTimedOut(): boolean {
        const elapsed = Date.now() - this.sessionStartTime;
        return elapsed > this.SESSION_TIMEOUT_THRESHOLD;
    }

    /**
     * Gets the elapsed time since session start
     */
    public getElapsedTime(): number {
        return Date.now() - this.sessionStartTime;
    }

    /**
     * Executes an operation with timeout protection
     */
    public async executeWithTimeout<T>(
        operation: () => Promise<T>,
        timeoutMs: number = 30000,
        operationName: string = 'operation'
    ): Promise<T> {
        logger.info(`Starting ${operationName} with ${timeoutMs}ms timeout`);
        
        try {
            const result = await Promise.race([
                operation(),
                new Promise<never>((_, reject) =>
                    setTimeout(() => reject(new Error(`${operationName} timed out after ${timeoutMs}ms`)), timeoutMs)
                )
            ]);
            
            logger.info(`${operationName} completed successfully`);
            return result;
        } catch (error) {
            logger.error(`${operationName} failed:`, error);
            throw error;
        }
    }

    /**
     * Safely executes a WebDriver operation with session monitoring
     */
    public async safeExecute<T>(
        operation: () => Promise<T>,
        operationName: string = 'WebDriver operation',
        timeoutMs: number = 30000
    ): Promise<T | null> {
        try {
            // Check if session is approaching timeout before starting
            if (this.hasSessionTimedOut()) {
                logger.warn(`Skipping ${operationName} - session has timed out`);
                return null;
            }

            if (this.isSessionApproachingTimeout()) {
                logger.warn(`${operationName} starting with session approaching timeout (${this.getElapsedTime()}ms elapsed)`);
            }

            return await this.executeWithTimeout(operation, timeoutMs, operationName);
        } catch (error) {
            logger.error(`Safe execution of ${operationName} failed:`, error);
            return null;
        }
    }

    /**
     * Attempts to recover from a session timeout or error
     */
    public async attemptSessionRecovery(): Promise<boolean> {
        try {
            logger.info('Attempting session recovery...');

            // Try to get current context to test session health
            const currentContext = await this.safeExecute(
                () => driver.getContext(),
                'get current context',
                5000
            );

            if (currentContext) {
                logger.info('Session appears to be healthy');
                return true;
            }

            // Try to get window handles as another health check
            const windows = await this.safeExecute(
                () => driver.getWindowHandles(),
                'get window handles',
                5000
            );

            if (windows && windows.length > 0) {
                logger.info('Session recovered - window handles available');
                return true;
            }

            logger.warn('Session recovery failed - no valid session state detected');
            return false;

        } catch (error) {
            logger.error('Session recovery attempt failed:', error);
            return false;
        }
    }

    /**
     * Performs a session health check
     */
    public async performHealthCheck(): Promise<boolean> {
        try {
            logger.info('Performing session health check...');

            // Quick health check operations
            const healthChecks = [
                () => driver.getTitle(),
                () => driver.getUrl(),
                () => driver.getContext(),
            ];

            for (const check of healthChecks) {
                const result = await this.safeExecute(check, 'health check', 3000);
                if (result !== null) {
                    logger.info('Session health check passed');
                    return true;
                }
            }

            logger.warn('Session health check failed');
            return false;

        } catch (error) {
            logger.error('Session health check error:', error);
            return false;
        }
    }

    /**
     * Resets the session timer
     */
    public resetSessionTimer(): void {
        this.sessionStartTime = Date.now();
        logger.info('Session timer reset');
    }

    /**
     * Logs session statistics
     */
    public logSessionStats(): void {
        const elapsed = this.getElapsedTime();
        const remainingTime = this.SESSION_TIMEOUT_THRESHOLD - elapsed;
        
        logger.info(`Session Stats:
            - Elapsed: ${elapsed}ms
            - Remaining safe time: ${remainingTime}ms
            - Approaching timeout: ${this.isSessionApproachingTimeout()}
            - Timed out: ${this.hasSessionTimedOut()}`);
    }
}

export default new SessionManager();
