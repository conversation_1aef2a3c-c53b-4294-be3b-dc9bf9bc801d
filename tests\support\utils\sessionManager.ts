import logger from './logger.util.ts';

/**
 * Session Manager utility for handling WebDriver session timeouts and recovery
 */
class SessionManager {
    private sessionStartTime: number = 0;
    private readonly SESSION_WARNING_THRESHOLD = 60000; // 60 seconds
    private readonly SESSION_TIMEOUT_THRESHOLD = 80000; // 80 seconds (before 90s Sauce Labs timeout)

    /**
     * Marks the start of a new session or operation
     */
    public markSessionStart(): void {
        this.sessionStartTime = Date.now();
        logger.info('Session operation started').catch(console.error);
    }

    /**
     * Checks if the session is approaching timeout
     */
    public isSessionApproachingTimeout(): boolean {
        const elapsed = Date.now() - this.sessionStartTime;
        return elapsed > this.SESSION_WARNING_THRESHOLD;
    }

    /**
     * Checks if the session has exceeded the safe timeout threshold
     */
    public hasSessionTimedOut(): boolean {
        const elapsed = Date.now() - this.sessionStartTime;
        return elapsed > this.SESSION_TIMEOUT_THRESHOLD;
    }

    /**
     * Gets the elapsed time since session start
     */
    public getElapsedTime(): number {
        return Date.now() - this.sessionStartTime;
    }

    /**
     * Executes an operation with timeout protection
     */
    public async executeWithTimeout<T>(
        operation: () => Promise<T>,
        timeoutMs: number = 30000,
        operationName: string = 'operation'
    ): Promise<T> {
        logger.info(`Starting ${operationName} with ${timeoutMs}ms timeout`).catch(console.error);

        try {
            const result = await Promise.race([
                operation(),
                new Promise<never>((_, reject) =>
                    setTimeout(() => reject(new Error(`${operationName} timed out after ${timeoutMs}ms`)), timeoutMs)
                )
            ]);

            logger.info(`${operationName} completed successfully`).catch(console.error);
            return result;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger.error(`${operationName} failed: ${errorMessage}`, {}).catch(console.error);
            throw error;
        }
    }

    /**
     * Safely executes a WebDriver operation with session monitoring
     */
    public async safeExecute<T>(
        operation: () => Promise<T>,
        operationName: string = 'WebDriver operation',
        timeoutMs: number = 30000
    ): Promise<T | null> {
        try {
            // Check if session is approaching timeout before starting
            if (this.hasSessionTimedOut()) {
                logger.info(`Skipping ${operationName} - session has timed out`).catch(console.error);
                return null;
            }

            if (this.isSessionApproachingTimeout()) {
                logger.info(`${operationName} starting with session approaching timeout (${this.getElapsedTime()}ms elapsed)`).catch(console.error);
            }

            return await this.executeWithTimeout(operation, timeoutMs, operationName);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger.error(`Safe execution of ${operationName} failed: ${errorMessage}`, {}).catch(console.error);
            return null;
        }
    }

    /**
     * Attempts to recover from a session timeout or error
     */
    public async attemptSessionRecovery(): Promise<boolean> {
        try {
            logger.info('Attempting session recovery...').catch(console.error);

            // Try to get current context to test session health
            const currentContext = await this.safeExecute(
                () => driver.getContext(),
                'get current context',
                5000,
            );

            if (currentContext) {
                logger.info('Session appears to be healthy').catch(console.error);
                return true;
            }

            // Try to get window handles as another health check
            const windows = await this.safeExecute(
                () => driver.getWindowHandles(),
                'get window handles',
                5000,
            );

            if (windows && windows.length > 0) {
                logger.info('Session recovered - window handles available').catch(console.error);
                return true;
            }

            logger.info('Session recovery failed - no valid session state detected').catch(console.error);
            return false;

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger.error(`Session recovery attempt failed: ${errorMessage}`, {}).catch(console.error);
            return false;
        }
    }

    /**
     * Performs a session health check
     */
    public async performHealthCheck(): Promise<boolean> {
        try {
            logger.info('Performing session health check...').catch(console.error);

            // Quick health check operations
            const healthChecks = [
                () => driver.getTitle(),
                () => driver.getUrl(),
                () => driver.getContext(),
            ];

            for (const check of healthChecks) {
                const result = await this.safeExecute(check, 'health check', 3000);
                if (result !== null) {
                    logger.info('Session health check passed').catch(console.error);
                    return true;
                }
            }

            logger.info('Session health check failed').catch(console.error);
            return false;

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger.error(`Session health check error: ${errorMessage}`, {}).catch(console.error);
            return false;
        }
    }

    /**
     * Resets the session timer
     */
    public resetSessionTimer(): void {
        this.sessionStartTime = Date.now();
        logger.info('Session timer reset').catch(console.error);
    }

    /**
     * Logs session statistics
     */
    public logSessionStats(): void {
        const elapsed = this.getElapsedTime();
        const remainingTime = this.SESSION_TIMEOUT_THRESHOLD - elapsed;

        logger.info(`Session Stats:
            - Elapsed: ${elapsed}ms
            - Remaining safe time: ${remainingTime}ms
            - Approaching timeout: ${this.isSessionApproachingTimeout()}
            - Timed out: ${this.hasSessionTimedOut()}`).catch(console.error);
    }
}

export default new SessionManager();
