import Page from './Page.ts';
import elementActions from '../support/actions/element.actions.ts';
import couponHomePageObject from '../pageObjects/CouponHome.pageObject.ts';
import HambergerMenuPageObjects from '../pageObjects/HamburgerMenu.pageObjects.ts';
import StoreDetailsPageObjects from '../pageObjects/StoreDetails.pageObjects.ts';
import { parse } from 'date-fns';
import locationServicePage from './locationService.page.ts';
import LocationServicesPageObjects from '../pageObjects/LocationServices.pageObjects.ts';
import AltHomePageObjects from '../pageObjects/AltHome.pageObjects.ts';
import { JsonTestDataHandler } from '../support/utils/JsonTestDataHandler.ts';
import assertionHelper, { AssertionHelper } from '../support/helpers/assertionHelper.ts';
import path from 'path';

class CouponHome extends Page {

    public async validateUserOnActiveCouponPage() {
        
        couponHomePageObject.allCoupons.forEach(async coupon => {
            await expect(await coupon).toBeDisplayed();
        });
    }

    public async validateUserOnCouponHomePage() {
        //Validate evrything on coupon Home Page
        //await elementActions.waitForClickable(await this.allCoupons);
        await expect(await couponHomePageObject.allCpns).toBeDisplayed();
        await expect(await couponHomePageObject.welcmFstNameTxt).toBeDisplayed();
        await expect(await couponHomePageObject.hmbrgrMenu).toBeDisplayed();
        await expect(await couponHomePageObject.btnSortOffers).toBeDisplayed();
        await expect(await couponHomePageObject.lblSortBy).toBeDisplayed();

        //await expect(await couponHomePageObject.couponsPrice).toBeDisplayed();
        //await expect(HambergerMenuPageObjects.grizzly_SGW_wrnig).toBeDisplayed();

        //await expect(await couponHomePageObject.couponsMessage).toBeDisplayed();
        //await expect(await couponHomePageObject.couponDescriptionStyle).toBeDisplayed();
        //await expect(await couponHomePageObject.couponsTnkuMesge).toBeDisplayed();
        //await expect(await couponHomePageObject.couponsExpDt).toBeDisplayed();
        await elementActions.click(couponHomePageObject.personalizedCoupon);
        await expect(await couponHomePageObject.btn_ChooseStore).toBeDisplayed();

    };

    async validateallCouponsandTextsOnCouponHomePage(Brand: string, filename: string, sheetname: string, scenarioname: string) {
        const SHEET_NAME = sheetname;
        const jsonFilePath = path.join(process.cwd(), 'data', filename);
        const testData = new JsonTestDataHandler(jsonFilePath);
        const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
        console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
       
        const brand = Brand.toUpperCase();
        if (brand === 'GRIZZLY') {
        expect(await elementActions.isDisplayed(HambergerMenuPageObjects.grizzly_SGW_wrnig)).toBeTruthy();
         const sgwWarning = testData.getCellValue(SHEET_NAME, scenarioname, 'sgwWarning');
         await  assertionHelper.mssgcomparision(HambergerMenuPageObjects.grizzly_SGW_wrnig, sgwWarning);
         expect(await elementActions.isDisplayed(couponHomePageObject.fixedWrng1)).toBeTruthy();
         const fixedWarning1 = testData.getCellValue(SHEET_NAME, scenarioname, 'fixedWrng1');
         await  assertionHelper.mssgcomparision(couponHomePageObject.fixedWrng1, fixedWarning1);
         expect(await elementActions.isDisplayed(couponHomePageObject.fixedWrng2)).toBeTruthy();
         const fixedWarning2 = testData.getCellValue(SHEET_NAME, scenarioname, 'fixedWrng2');
         await  assertionHelper.mssgcomparision(couponHomePageObject.fixedWrng2, fixedWarning2);
        }
       
       
         expect(await elementActions.isDisplayed(couponHomePageObject.brandLogo)).toBeTruthy();
         expect(await elementActions.isDisplayed(couponHomePageObject.lblSortBy)).toBeTruthy();
         const sortTxt = testData.getCellValue(SHEET_NAME, scenarioname, 'sortByTxt');
         await  assertionHelper.mssgcomparision(couponHomePageObject.sortByTxt, sortTxt);
         expect(await elementActions.isDisplayed(couponHomePageObject.hmbrgrMenu)).toBeTruthy();
         expect(await elementActions.isDisplayed(couponHomePageObject.btnSortOffers)).toBeTruthy();
         expect(await elementActions.isDisplayed(couponHomePageObject.couponCount)).toBeTruthy();
        const coupons = await driver.$$('//img[@alt="coupons image"]');
        console.log(`Number of Coupons: ${coupons.length}`);
 
        for (let i = 0; i < await coupons.length; i++) {
            const coupon = coupons[i];
           
            await coupon.click();
            await assertionHelper.assertElementDisplayed(couponHomePageObject.getcouponsPrice(i+1));
            await assertionHelper.assertElementDisplayed(couponHomePageObject.getcouponsImage(i+1));
            await assertionHelper.assertElementDisplayed(couponHomePageObject.getcouponsExpDate(i+1));
           
            await assertionHelper.assertElementDisplayed(couponHomePageObject.getcouponsOf1CpnTxt(i+1));
            await assertionHelper.assertElementDisplayed(couponHomePageObject.getcouponsGdStlTxt(i+1));
            await assertionHelper.assertElementDisplayed(couponHomePageObject.getcouponsOfrVldTxt(i+1));
            await assertionHelper.assertElementDisplayed(couponHomePageObject.getcouponsPrgrsBar(i+1));
            await assertionHelper.assertElementDisplayed(couponHomePageObject.getcouponsDaysLeft(i+1));
            await assertionHelper.assertElementDisplayed(couponHomePageObject.getcouponsChooseStore(i+1));
           
           
        }
       
        await assertionHelper.assertElementDisplayed(couponHomePageObject.termRemptnTxt);
        const redemption = testData.getCellValue(SHEET_NAME, scenarioname, 'redemptionTxt');
         await  assertionHelper.mssgcomparision(couponHomePageObject.termRemptnTxt, redemption);
 
        expect(await elementActions.isDisplayed(couponHomePageObject.termsHeading)).toBeTruthy();
        const couponTermsHeading = testData.getCellValue(SHEET_NAME, scenarioname, 'termsHeading');
        await  assertionHelper.mssgcomparision(couponHomePageObject.termsHeading, couponTermsHeading);
 
        expect(await elementActions.isDisplayed(couponHomePageObject.voidText)).toBeTruthy();
        const voidTxt = testData.getCellValue(SHEET_NAME, scenarioname, 'voidTxt');
        await  assertionHelper.mssgcomparision(couponHomePageObject.voidText, voidTxt);
 
        expect(await elementActions.isDisplayed(couponHomePageObject.couponTerms)).toBeTruthy();
        const couponTerms = testData.getCellValue(SHEET_NAME, scenarioname, 'termsBeforeReadMore');
        await  assertionHelper.mssgcomparision(couponHomePageObject.couponTerms, couponTerms);
 
        expect(await elementActions.isDisplayed(couponHomePageObject.readMore)).toBeTruthy();
        const readMoreText = testData.getCellValue(SHEET_NAME, scenarioname, 'readMoreTxt');
        await  assertionHelper.mssgcomparision(couponHomePageObject.readMore, readMoreText);
        await elementActions.click(couponHomePageObject.readMore);
 
        await assertionHelper.assertElementDisplayed(couponHomePageObject.couponAllTerms);
        const couponAllTermsTxt = testData.getCellValue(SHEET_NAME, scenarioname, 'termsAfterReadMore');
        //await  assertionHelper.mssgcomparision(couponHomePageObject.couponAllTerms, couponAllTermsTxt);;
        const element =await couponHomePageObject.couponAllTerms;
        const successMessage = (await (element).getText()).replace(/[\r\n]+/g, '');
        const actualsuccessmessage = (await successMessage).trim();
        console.log('Generated Text is: ', actualsuccessmessage);
        console.log('Expected Text is: ', couponAllTermsTxt);
        expect(actualsuccessmessage).toEqual(couponAllTermsTxt.trim());
 
        expect(await elementActions.isDisplayed(couponHomePageObject.readLess)).toBeTruthy();
        const readLessText = testData.getCellValue(SHEET_NAME, scenarioname, 'readLessTxt');
        await  assertionHelper.mssgcomparision(couponHomePageObject.readLess, readLessText);
 
      }

    public async validateWeeklyCoupon(coupontype: string): Promise<void> {
        await driver.pause(3000);
        await expect(await couponHomePageObject.brandLogo).toBeDisplayed();
        await expect(await couponHomePageObject.couponCount).toBeDisplayed();
        await driver.pause(3000);

        if (coupontype.includes('WEEKLY')) {
            await expect(await couponHomePageObject.weeklyCpn).toBeDisplayed();
            await elementActions.click(couponHomePageObject.weeklyCpn);
        }
        else if (coupontype.includes('WELCOME')) {
            await expect(await couponHomePageObject.welComeCpn).toBeDisplayed();
            await elementActions.click(couponHomePageObject.welComeCpn);
        }
        else if (coupontype.includes('BIRTHDAY')) {
            await expect(await couponHomePageObject.birthDayCpn).toBeDisplayed();
            await elementActions.click(couponHomePageObject.birthDayCpn);
        }
        else if (coupontype.includes('THANK')) {
            await expect(await couponHomePageObject.thankYouCpn).toBeDisplayed();
            await elementActions.click(couponHomePageObject.thankYouCpn);
        }
        await driver.pause(3000);
        await expect(await couponHomePageObject.btn_ChooseStore).toBeDisplayed();
        await expect(await couponHomePageObject.couponsPrice).toBeDisplayed();
        await expect(await couponHomePageObject.couponsImg).toBeDisplayed();
        await expect(await couponHomePageObject.offerVlidTxt).toBeDisplayed();
        await expect(await couponHomePageObject.couponsProgressBar).toBeDisplayed();
        await expect(await couponHomePageObject.daysLeft).toBeDisplayed();


    };

    public async validateEitherORCoupons() {

        await expect(await couponHomePageObject.either_Coupon1).toBeDisplayed();
        await expect(await couponHomePageObject.either_OrIcon).toBeDisplayed();
        await expect(await couponHomePageObject.either_Coupon2).toBeDisplayed();

        await elementActions.click(couponHomePageObject.either_Coupon1);

        await expect(await couponHomePageObject.btn_ChooseStore).toBeDisplayed();
        await expect(await couponHomePageObject.couponsPrice).toBeDisplayed();
        await expect(await couponHomePageObject.couponsImg).toBeDisplayed();
        await expect(await couponHomePageObject.offerVlidTxt).toBeDisplayed();
        await expect(await couponHomePageObject.couponsProgressBar).toBeDisplayed();
        await expect(await couponHomePageObject.daysLeft).toBeDisplayed();

        await expect(await couponHomePageObject.coupon_Arrow1).toBeDisplayed();
        await elementActions.click(couponHomePageObject.coupon_Arrow1);

        await expect(await couponHomePageObject.coupon_Arrow2).toBeDisplayed();
        await elementActions.click(couponHomePageObject.coupon_Arrow2);

    };


    public async validateerrorMsgPopupforRestrictedLoc(brand: string, location: string): Promise<void> {

        //const errmsg_ZipCodeInvalid = `WE ARE SORRY, BUT GRIZZLY NICOTINE POUCHES COUPONS (AND SIMILAR OFFERS) ARE NOT AVAILABLE IN THIS ZIPCODE, ${county}, NEW YORK AT THIS TIME.`;

        const errmsg_ZipCodeInvalid = `We are sorry, but ${brand} coupons (and similar offers) are not available in ${location} at this time.`;
       
        await driver.pause(3000);
        //Validate the Actual and Expected Text 
        await elementActions.isDisplayed(StoreDetailsPageObjects.errormsg_PopupStoreList);
        const successMessage = (await StoreDetailsPageObjects.errormsg_PopupStoreList).getText();
        const actualsuccessmessage = (await successMessage);
        console.log('Generated Text is: ', actualsuccessmessage);
        console.log('Expected Text is: ', errmsg_ZipCodeInvalid);
        expect(actualsuccessmessage).toEqual(errmsg_ZipCodeInvalid);
        console.log('Success Message is Displayed Successfully');

        await driver.pause(3000);
        //Close the Popup
        await elementActions.highlightElement(StoreDetailsPageObjects.errmsgCloseBtn);
        await expect(await StoreDetailsPageObjects.errmsgCloseBtn).toBeDisplayed();
        await elementActions.click(StoreDetailsPageObjects.errmsgCloseBtn);
        console.log('Store List Page - Close popup button');

    };

    public async validateCouponHomePageTerms() {
        const cpnTerms = 'Offer restricted to eligible age 21+ tobacco consumers. Consumer must show valid form of ID. Limited-time offer. Offer is subject to termination or change without notice. Consumer may pay sales tax.';
        console.log(cpnTerms);
        await driver.pause(3000);

        //Validate the Actual and Expected Text 
        await elementActions.isDisplayed(couponHomePageObject.couponTerms);
        const successMessage = (await couponHomePageObject.couponTerms).getText();
        const actualsuccessmessage = (await successMessage);
        console.log('Generated Text is: ', actualsuccessmessage);
        console.log('Expected Text is: ', cpnTerms);
        expect(actualsuccessmessage).toEqual(cpnTerms);
        console.log('Coupon Terms Displayed Successfully');

        await driver.pause(5000);
        await elementActions.highlightElement(couponHomePageObject.readMore);
        await expect(await couponHomePageObject.readMore).toBeDisplayed();
        await elementActions.click(couponHomePageObject.readMore);
        await driver.pause(5000);
        await elementActions.highlightElement(couponHomePageObject.readLess);
        await expect(await couponHomePageObject.readLess).toBeDisplayed();
        await elementActions.click(couponHomePageObject.readLess);
        await driver.pause(5000);

    };

    public async validateCouponSort() {

        await expect(await couponHomePageObject.personalizedCoupon).toBeDisplayed();
        await elementActions.click(couponHomePageObject.btnSortOffers);
        await driver.pause(5000);

        async function checkCouponExpiry() {
            const couponExpiry = await $$('//div[text()="Expires "]'); // Adjust the selector as needed
            const dates: Date[] = [];

            for (let i = 0; i < await couponExpiry.length; i++) {
                const expiryDte = (await couponExpiry[i].getText()).split(' ');
                const dateStr = `${expiryDte[1]} ${expiryDte[3]} ${expiryDte[4]}`;
                const date = parse(dateStr, 'MM/dd/yy h:mm a', new Date());
                dates.push(date);
            }


            let expiryCheck = true;
            for (let i = 0; i < dates.length - 1; i++) {
                if (!(dates[i].getTime() === dates[i + 1].getTime() || dates[i].getTime() < dates[i + 1].getTime())) {
                    expiryCheck = false;
                    break;
                }
            }

            if (!expiryCheck) {
                console.log(`The coupons are NOT sorted based on expiry Date: ${dates}`);
            }
            else {
                console.log(`The coupons are sorted based on expiry Date successfully: ${dates}`);
            }
            console.log(`Coupons displayed after sorting by expiry: ${dates}`);
        }

        // Call the function to check coupon expiry
        checkCouponExpiry();

    };

    public async addingpwalink(Brand: string): Promise<void> {

        if (await driver.isIOS) {

            await driver.switchContext("NATIVE_APP");
            await driver.$("//*[@label='Share']").click();
            await driver.execute("mobile:scroll", { direction: "down" });

            const element = await driver.waitUntil(
                async () => (await driver.$("//*[@label='Add to Home Screen']/XCUIElementTypeOther[2]")).isDisplayed(),
                { timeout: 10000 }
            );

            await driver.$("//*[@label=\"Add to Home Screen\"]/XCUIElementTypeOther[2]").click();
            await driver.$("//*[@label=\"Clear text\"]").click();
            await driver.$("//XCUIElementTypeTable//XCUIElementTypeTextField").setValue(Brand);
            await driver.$("//*[@label=\"Add\"]").click();
        }
    };

    public async launchingAppName(appName: string, appActivity: string): Promise<void> {
        if (await driver.isIOS) {
            console.log("Current context while launching:", await driver.getContext());

            let scrolls = 5;

            do {
                const icon = await driver.$(`//XCUIElementTypeIcon[contains(@label,'${appName}')]`);

                if (!(await icon.isDisplayed())) {
                    scrolls--;

                    await driver.performActions([
                        {
                            type: "pointer",
                            id: "finger1",
                            parameters: { pointerType: "touch" },
                            actions: [
                                { type: "pointerMove", duration: 0, x: 300, y: 800 }, // Start position
                                { type: "pointerDown", button: 0 },
                                { type: "pause", duration: 500 },
                                { type: "pointerMove", duration: 1000, x: 300, y: 200 }, // Swipe up
                                { type: "pointerUp", button: 0 }
                            ]
                        }
                    ]);
                } else {
                    break;
                }
            } while (scrolls > 0);

            const shortcutIcon = await driver.$(`//XCUIElementTypeIcon[contains(@label,'${appName}')]`);
            await shortcutIcon.click();

            

            await driver.pause(5000); // Equivalent to Thread.sleep(5000)

        }

    }

    async navigatetoofferspage(brand:string):Promise<void>
    {
        if (driver.isIOS) {
            
            await expect(await LocationServicesPageObjects.redeemnowbtn).toBeDisplayed();
        await elementActions.click(LocationServicesPageObjects.redeemnowbtn);
        }
    }

}




export default new CouponHome();



