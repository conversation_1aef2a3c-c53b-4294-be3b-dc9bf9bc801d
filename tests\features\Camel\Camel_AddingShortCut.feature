Feature: Camel PWA - Add PWA ShortCut
    As a RJR user
    I want to access and add the shortcut across different pages of PWA
    
    @CouponHomePageAddShortcut
    Scenario Outline: Validate Camel PWA short cut adding on coupon home page
      Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        When I navigate and add <Brand> PWA link
        #Then I Add PWA link pop up
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>        

@CouponHomePageShortcut_QAstage
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|

@CamelCouponHomePageShortcut_Prod
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL     | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|


@StoreDetailsAddingshortcut
Scenario Outline: Validate user is able to get the <Brand> pwa notification on store details page and able to add it in the device.
Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        When I select a coupon and click on choose a store button
        Then I select a store from store list page
        When I navigate and add <Brand> PWA link
        #Then I Add PWA link pop up
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>        

@StoredetailsaddingShortcut_QAstage
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|

@StoredetailsaddingShortcut_Prod
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|    


@StorelistAddingshortcut
Scenario Outline: Validate user is able to get the <Brand> pwa notification on store list page and able to add it in the device.
Given I am on the <Brand> login page at <URL>
And I set my location to "<Latitude>" and "<Longitude>"
When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
Then I click the "Understood" button
When I navigate to <Brand> offers page
Then I should be on the Coupons Home page
When I select a coupon and click on choose a store button
When I navigate and add <Brand> PWA link
When I launch the PWA App <appName> and app activity as <appActivity>
Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>

@StoreListaddingShortcut_QAstage
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|

@StoreListaddingShortcut_Prod
Examples:
| Brand     |  URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|    


@StorelistMapitAddingshortcut
Scenario Outline: Validate user is able to get the <Brand> pwa notification on map it page and able to add it in the device.
Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        When I select a coupon and click on choose a store button
        Then I click on Map it link on first store
        When I navigate and add <Brand> PWA link
        #Then I Add PWA link pop up
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>        

@StoreListMapitaddingShortcut_QAstage
Examples:
| Brand    | URL                          | Username                                     | Password  | Latitude          | Longitude          |appName|
|CAMEL     |  https://mobile-qa.camel.com/| <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|

@StoreListMapitaddingShortcut_Prod
Examples:
| Brand     | URL                       | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL     | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|   


@ShowdirectionsAddingshortcut
Scenario Outline: Validate user is able to get the <Brand> pwa notification on show directions page and able to add it in the device.
Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        When I select a coupon and click on choose a store button
        Then I click on Map it link on first store available
        When I click on show directions button
        When I navigate and add <Brand> PWA link
        #Then I Add PWA link pop up
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>        

@CamelShowdirectionsaddingShortcut_QAstage
Examples:
| Brand     | URL                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL     | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|

@CamelShowdirectionaddingShortcut_Prod
Examples:
| Brand     | URL                       | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL     | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|    


@ShowdirectionsAddingshortcut
Scenario Outline: Validate user is able to get the <Brand> pwa notification on map view page and able to add it in the device.
Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        When I select a coupon and click on choose a store button
        Then I navigate to store list map view page
        When I navigate and add <Brand> PWA link
        #Then I Add PWA link pop up
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>        

@CamelMapviewaddingShortcut_QAstage
Examples:
| Brand     | URL                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL     | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|

@CamelMapviewaddingShortcut_Prod
Examples:
| Brand     | URL                       | Username                                     | Password  | Latitude          | Longitude          |appName|
| CAMEL     | https://mobile.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|     