import path from 'path';
import { error } from 'console';

import CouponHomePageObject from '../pageObjects/CouponHome.pageObject.ts';
import logger from '../support/utils/logger.util.ts';
import { JsonTestDataHandler } from '../support/utils/JsonTestDataHandler.ts';
import assertionHelper from '../support/helpers/assertionHelper.ts';
import couponHomePage from './couponHome.page.ts';
import GoogleVisionUtil from '../support/utils/googleVisionUtil.ts';
//import { google } from '@google-cloud/vision/build/protos/protos.js';
 
 
class SGW {
 
    async getQuarterMonth(month: number) {
 
        if (month >= 1 && month <= 3) {
            return "Q2";
        } else if (month >= 4 && month <= 6) {
            return "Q3";
        } else if (month >= 7 && month <= 9) {
            return "Q4";
        } else if (month >= 10 && month <= 12) {
            return "Q1";
        } else {
            return "Invalid month";
        }
    }
 
    async getQuarterMonthProd(month: number) {
 
        if (month >= 1 && month <= 3) {
            return "Q1";
        } else if (month >= 4 && month <= 6) {
            return "Q2";
        } else if (month >= 7 && month <= 9) {
            return "Q3";
        } else if (month >= 10 && month <= 12) {
            return "Q4";
        } else {
            return "Invalid month";
        }
    }
 
    async compareSQWText(filename: string, sheetname: string, scenarioname: string) {
        try {
            const currentDate = new Date();
            const month = currentDate.getMonth();
            const url = browser.getUrl();
            let quarter = '';
            if ((await url).includes("qa")) {
                quarter = await this.getQuarterMonth(month);
            } else {
                quarter = await this.getQuarterMonthProd(month);
            }
            const visionUtil = new GoogleVisionUtil();
            let sgwText = '';
            let Q1 = '';
            let Q2 = '';
            let Q3 = '';
            let Q4 = '';
            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            if ((await url).includes('camel')) {
 
                Q1 = testData.getCellValue(SHEET_NAME, scenarioname, 'camelSGWQ1');
                Q2 = testData.getCellValue(SHEET_NAME, scenarioname, 'camelSGWQ2');
                Q3 = testData.getCellValue(SHEET_NAME, scenarioname, 'camelSGWQ3');
                Q4 = testData.getCellValue(SHEET_NAME, scenarioname, 'camelSGWQ4');
 
                //sgwText = await visionUtil.detectTextFromElementSingleLine(CouponHomePageObject.camelSGWWarning);
            }
            else {
                Q1 = testData.getCellValue(SHEET_NAME, scenarioname, 'sgwQ1');
                Q2 = testData.getCellValue(SHEET_NAME, scenarioname, 'sgwQ2');
                Q3 = testData.getCellValue(SHEET_NAME, scenarioname, 'sgwQ3');
                Q4 = testData.getCellValue(SHEET_NAME, scenarioname, 'sgwQ4');
                
                
                sgwText = await visionUtil.detectTextFromElementSingleLine(CouponHomePageObject.camelSGWWarning);
            }
            console.log("Actual SGW Text:", sgwText);
            console.log("Expected SGW Text", Q1);
            if (await quarter == 'Q1') {
 
                await assertionHelper.sgwTextcomparision(sgwText, Q1);
                console.log('Validated SGW Message:', Q1);
            }
            else if (await quarter == 'Q2') {
                await assertionHelper.sgwTextcomparision(sgwText, Q2);
                console.log('Validated SGW Message:', Q2);
            }
            else if (await quarter == 'Q3') {
                await assertionHelper.sgwTextcomparision(sgwText, Q3);
                console.log('Validated SGW Message:', Q3);
            }
            else if (await quarter == 'Q4') {
                await assertionHelper.sgwTextcomparision(sgwText, Q4);
                console.log('Validated SGW Message:', Q4);
            } else {
                logger.error('Invalid Quarter', { error });
            }
 
        } catch (error) {
            logger.error('Unable to Validate SGW Message', { error });
            throw error;
        }
    }
 
 
}
export default new SGW();