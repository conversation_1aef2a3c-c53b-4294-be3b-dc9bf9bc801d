@GrizzlyMOLoginFunctionality
Feature: Grizzly MO PWA - Login Functionalities
  As an RJR user, I should be able to validate the Grizzly MO Login page functionality

 
  @GrizzlyMOInvalidLoginHome
  Scenario Outline: Validate error message on invalid login credentials
    Given I am on the login page for <Brand> with URL <URL>
    And I set the device location to <Latitude> and <Longitude>
    When I attempt to login with invalid credentials username <Username> and password <Password>
    Then I should see an error message indicating invalid login credentials

    @GrizzlyMOInvalidLoginHome_QA
    Examples:
      | Brand   | URL                                                              | Username                     | Password  | Latitude          | Longitude          |
      | GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |

  @GrizzlyMOUsernameAfterLogout
  Scenario Outline: Validate username persistence with Remember Me checkbox after logout
    Given I am on the login page for <Brand> with URL <URL>
    And I set the device location to <Latitude> and <Longitude>
    When I attempt to login Remember me Checkbox with valid credentials username <Username> and password <Password>
    Then I click the "Understood" button
    When I navigate to Hamburger Menu
    Then I validate the elements in Grizzly Hamburger Menu
    When I click on Grizzly Logout link
    Then I should see my <Username> persisted in the login field

    @GrizzlyMOUsernameAfterLogout_QA
    Examples:
      | Brand   | URL                                          | Username                         | Password  | Latitude          | Longitude          |
      | GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |

  @GrizzlyMOLogin128
  Scenario Outline: Validate login functionality with 128 character username
    Given I am on the login page for <Brand> with URL <URL>
    And I set the device location to <Latitude> and <Longitude>
    When I login with 128 character username <Username> and password <Password>
    Then I click the "Understood" button
    When I navigate to Hamburger Menu
    Then I validate the elements in Grizzly Hamburger Menu
    When I click on Grizzly Logout link
    
    @GrizzlyMOLogin128_QA
    Examples:
      | Brand   | URL                                          | Username                                                                                                                        | Password  | Latitude          | Longitude          |
      | GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |

  @GrizzlyMOAirplaneModeOn
  Scenario Outline: Validate login behavior with Airplane mode enabled
    Given I am on the login page for <Brand> with URL <URL>
    And I set the device location to <Latitude> and <Longitude>
    When I login with username <Username> and password <Password>
    And I enable the Airplane mode
    Then I should see an appropriate connectivity error message

    @GrizzlyMOAirplaneModeOn_QA
    Examples:
      | Brand   | URL                                          | Username                     | Password  | Latitude          | Longitude          |
      | GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |


