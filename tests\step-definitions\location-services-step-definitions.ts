import { Then, When } from '@wdio/cucumber-framework';
import logger from '../support/utils/logger.util.ts';
import locationServicePage from '../pages/locationService.page.ts';


When(/^I click the Understood button and Don't allow the Location$/, async function () {
    await locationServicePage.clickUnderStoodButtonWithoutAllowLocation();
    console.log('When I click the Understood button and Do not allow the Location');
    await logger.info('When I click the Understood button and Do not allow the Location');
});


When(/^I click the Back button$/, async function () {
    await locationServicePage.clickBackBtnLocationServicesPage();
    console.log('When I click the Back Button on Location Services Page');
    await logger.info('When I click the Back Button on Location Services Page');
});

Then(/^I validate the Location Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath :string,sheetname :string, scenarioname :string) {
    await locationServicePage.locationPageValidation(filepath,sheetname,scenarioname);
    console.log('Validate all texts in the Location Service Page');
    await logger.info('Validate all texts in the Location Service Page');
});
 
Then(/^I Validate Location Disabled screen and click continue with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath :string,sheetname :string, scenarioname :string) {
    await locationServicePage.locationDisableScreen1(filepath,sheetname,scenarioname);
    console.log('Validate all texts in the Location Disable Page1');
    await logger.info('Validate all texts in the Location Disable Page2');
});
 
When(/^I Validate Location Disabled screen2 with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath :string,sheetname :string, scenarioname :string) {
    await locationServicePage.locationDisableScreen2(filepath,sheetname,scenarioname);
    console.log('Validate all texts in the Location Disable Page2');
    await logger.info('Validate all texts in the Location Disable Page2');
});
 
Then(/^I Validate Location Disabled screen3 with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath :string,sheetname :string, scenarioname :string) {
    await locationServicePage.locationDisableScreen3(filepath,sheetname,scenarioname);
    console.log('Validate all texts in the Location Disable Page3');
    await logger.info('Validate all texts in the Location Disable Page3');
});
 
When(/^I Validate Location Disabled screen4 with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath :string,sheetname :string, scenarioname :string) {
    await locationServicePage.locationDisableScreen4(filepath,sheetname,scenarioname);
    console.log('Validate all texts in the Location Disable Page4');
    await logger.info('Validate all texts in the Location Disable Page4');
});
 
Then(/^I Validate Location Disabled screen5 with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath :string,sheetname :string, scenarioname :string) {
    await locationServicePage.locationDisableScreen5(filepath,sheetname,scenarioname);
    console.log('Validate all texts in the Location Disable Page5');
    await logger.info('Validate all texts in the Location Disable Page5');
});
 
 
When(/^I Validate Location Disabled Last Screen and Click on TryAgain Button with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath :string,sheetname :string, scenarioname :string) {
    await locationServicePage.locationDisableLastScreenandClickOnTryAgain(filepath,sheetname,scenarioname);
    console.log('Validate all texts in the Location Disable LastPage and Click on TryAgain Button');
    await logger.info('Validate all texts in the Location Disable LastPage and Click on TryAgain Button');
});
 
When(/^I Validate Location Disabled Last Screen and Click on BackToGrizzly Button with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath :string,sheetname :string, scenarioname :string) {
    await locationServicePage.locationDisableLastScreenandClickOnBackToGrizzly(filepath,sheetname,scenarioname);
    console.log('Validate all texts in the Location Disable LastPage and Click on BackToGrizzly Button');
    await logger.info('Validate all texts in the Location Disable LastPage and Click on BackToGrizzly Button');
});
