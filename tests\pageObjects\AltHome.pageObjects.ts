import { $ } from '@wdio/globals';
import Page from '../pages/Page.ts';

class AltHomePwaPageObjects extends Page {
    //validateUserOnActiveCouponPage: any;

    get brandLogo() { return $('img[alt="Navbar Logo"]'); }
    get redeemNowBtn() { return $('button[data-testid="altHomeRedeemNow"]'); }
    get navBarLogo() { return $('img[alt="Navbar Logo"]'); }
    get couponCount() { return $('span[data-testid="coupons-count-value"]'); }
    get welcomeBackMsg() { return $("#welcomeContainer p"); }
    get btnpushnotificationsallow() { return $('div[data-testid="modalButtonTitle"]'); }

    get warningText() { return $('//p[contains(text(),"WARNING: This product contains nicotine. Nicotine is an addictive chemical.")]'); }
    get underageSaleText() { return $('//p[contains(text(),"UNDERAGE SALE PROHIBITED")]'); }
    get nicotineProductsText() { return $('//span[contains(text(),"NICOTINE PRODUCTS")]'); }

    get sgwcamel() {return $('//img[contains(@src, "warningcontent/images/q3/248.png")]');}
    get tile1Image() { return $('img[alt="tile1"]'); }
    get tile2Image() { return $('img[alt="tile2"]'); }
    get tile3Image() { return $('img[alt="tile3"]'); }


}


export default new AltHomePwaPageObjects();