import * as fs from 'fs';
import * as path from 'path';
import GoogleVisionUtil, { TextDetectionResult } from './googleVisionUtil.ts';
import logger from './logger.util.ts';

/**
 * Utility class for OCR-based text detection and clicking
 */
class OcrClickUtil {
  private visionUtil: GoogleVisionUtil;

  constructor() {
    this.visionUtil = new GoogleVisionUtil();
  }

  /**
   * Clicks on text found in the current screen using proper OCR with bounding boxes
   * @param textToFind - The text to find and click on
   * @param maxAttempts - Maximum number of attempts to find the text (default: 3)
   * @param waitBetweenAttempts - Time to wait between attempts in ms (default: 1000)
   * @param caseSensitive - Whether search should be case sensitive (default: false)
   * @returns Promise<boolean> - True if text was found and clicked, false otherwise
   */
  public async clickOnText(
    textToFind: string,
    maxAttempts: number = 3,
    waitBetweenAttempts: number = 1000,
    caseSensitive: boolean = false,
  ): Promise<boolean> {
    logger.info(`Attempting to find and click on text: "${textToFind}"`);

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        logger.info(`Attempt ${attempt}/${maxAttempts} to find text: "${textToFind}"`);

        // Take screenshot of the current screen
        const timestamp = new Date().getTime();
        const screenshotName = `ocr_click_${timestamp}.png`;
        const screenshotPath = path.resolve(process.cwd(), 'reports/screenshots', screenshotName);

        // Ensure directory exists
        const dir = path.dirname(screenshotPath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }

        // Take screenshot and save it
        await browser.saveScreenshot(screenshotPath);
        logger.info(`Screenshot saved to: ${screenshotPath}`);

        // Use OCR to find text with bounding boxes
        const matchingTexts = await this.visionUtil.findTextWithBoundingBoxes(
          screenshotPath,
          textToFind,
          caseSensitive,
          true, // Delete screenshot after processing
        );

        if (matchingTexts.length > 0) {
          // Use the first matching text element
          const targetText = matchingTexts[0];
          logger.info(`Found "${textToFind}" at coordinates: x=${targetText.boundingBox.x}, y=${targetText.boundingBox.y}, width=${targetText.boundingBox.width}, height=${targetText.boundingBox.height}`);

          // Calculate the center point of the text bounding box
          const clickX = targetText.boundingBox.x + Math.floor(targetText.boundingBox.width / 2);
          const clickY = targetText.boundingBox.y + Math.floor(targetText.boundingBox.height / 2);

          logger.info(`Clicking at coordinates: (${clickX}, ${clickY})`);

          // Click at the calculated coordinates
          if (browser.isMobile) {
            await browser.touchAction([
              { action: 'tap', x: clickX, y: clickY },
            ]);
            logger.info(`Tapped at (${clickX}, ${clickY}) where "${textToFind}" was found`);
          } else {
            // For desktop, use mouse click
            await browser.performActions([{
              type: 'pointer',
              id: 'mouse',
              parameters: { pointerType: 'mouse' },
              actions: [
                { type: 'pointerMove', duration: 0, x: clickX, y: clickY },
                { type: 'pointerDown', button: 0 },
                { type: 'pointerUp', button: 0 },
              ],
            }]);
            logger.info(`Clicked at (${clickX}, ${clickY}) where "${textToFind}" was found`);
          }

          return true;
        } else {
          logger.info(`Could not find "${textToFind}" in attempt ${attempt}`);

          // Fallback: Try JavaScript approach to find and click elements
          try {
            const jsClickResult = await browser.execute(`
              // Try to find elements containing the text
              const elements = Array.from(document.querySelectorAll('*'))
                .filter(el => el.textContent && el.textContent.includes('${textToFind}') &&
                       (window.getComputedStyle(el).display !== 'none'));

              // Click the first visible element found
              if (elements.length > 0) {
                elements[0].click();
                return true;
              }
              return false;
            `);

            if (jsClickResult) {
              logger.info(`Successfully clicked "${textToFind}" using JavaScript fallback`);
              return true;
            }
          } catch (jsError) {
            logger.info(`JavaScript fallback approach failed: ${jsError}`);
          }

          if (attempt < maxAttempts) {
            logger.info(`Waiting ${waitBetweenAttempts}ms before next attempt`);
            await new Promise(resolve => setTimeout(resolve, waitBetweenAttempts));
          }
        }
      } catch (error) {
        logger.error(`Error in attempt ${attempt} to find text: ${error}`);

        if (attempt < maxAttempts) {
          logger.info(`Waiting ${waitBetweenAttempts}ms before next attempt`);
          await new Promise(resolve => setTimeout(resolve, waitBetweenAttempts));
        }
      }
    }

    logger.error(`Failed to find and click on text "${textToFind}" after ${maxAttempts} attempts`);
    return false;
  }

  /**
   * Gets all text elements with their bounding boxes from the current screen
   * @returns Promise<TextDetectionResult[]> - Array of text detection results with bounding boxes
   */
  public async getAllTextWithBoundingBoxes(): Promise<TextDetectionResult[]> {
    try {
      // Take screenshot of the current screen
      const timestamp = new Date().getTime();
      const screenshotName = `ocr_analysis_${timestamp}.png`;
      const screenshotPath = path.resolve(process.cwd(), 'reports/screenshots', screenshotName);

      // Ensure directory exists
      const dir = path.dirname(screenshotPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Take screenshot and save it
      await browser.saveScreenshot(screenshotPath);
      logger.info(`Screenshot saved for analysis: ${screenshotPath}`);

      // Get all text with bounding boxes
      const allTexts = await this.visionUtil.detectTextWithBoundingBoxes(screenshotPath, true);

      logger.info(`Found ${allTexts.length} text elements on screen`);
      return allTexts;
    } catch (error) {
      logger.error(`Error getting all text with bounding boxes: ${error}`);
      throw error;
    }
  }
}

export default new OcrClickUtil();