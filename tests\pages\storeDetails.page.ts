import { browser } from '@wdio/globals';
import Page from './Page.ts';
import elementActions from '../support/actions/element.actions.ts';
import CouponRedemptionPageObject from '../pageObjects/CouponRedemption.pageObject.ts';
import StoreDetailsPageObjects from '../pageObjects/StoreDetails.pageObjects.ts';

class storeDetails extends Page {
   
    public async clickonLoadMoreandValidateStoreList() {
        await elementActions.highlightElement(StoreDetailsPageObjects.btnLoadMore);     
        await elementActions.click(StoreDetailsPageObjects.btnLoadMore);
        
        const stores = await driver.$$('//div[@data-testid="storesListItem"]');
        console.log(`Number of stores: ${stores.length}`);
        
    }

    public async selectStoreFromMapView() {
        await driver.pause(2000);
        //Select Store From Map View
        const MapIcon = await StoreDetailsPageObjects.lnkSelectStrMapViw;
            await browser.execute('arguments[0].click();', MapIcon);
       
        await driver.pause(3000);
        await elementActions.highlightElement(StoreDetailsPageObjects.lnkRightArrowMpViw);     
        await elementActions.click(StoreDetailsPageObjects.lnkRightArrowMpViw);
        await driver.pause(3000);
        
    }

    public async validateMapViewPage() {
        await driver.pause(2000);
        //Validate Map View Page
        const MapIcon = await StoreDetailsPageObjects.lnkSelectStrMapViw;
            await browser.execute('arguments[0].click();', MapIcon);
       
        await driver.pause(3000);
    
        await elementActions.click(StoreDetailsPageObjects.lnkRightArrowMpViw);
        await driver.pause(3000);
        
        await expect(await StoreDetailsPageObjects.downArrowRdmPg).toBeDisplayed();
        await elementActions.click(StoreDetailsPageObjects.downArrowRdmPg);

        const MapIcon1 = await StoreDetailsPageObjects.lnkSelectStrMapViw;
        await browser.execute('arguments[0].click();', MapIcon1);
        await expect(await StoreDetailsPageObjects.closeBtnMapVw).toBeDisplayed();
        await elementActions.click(StoreDetailsPageObjects.closeBtnMapVw);

        const MapIcon2 = await StoreDetailsPageObjects.lnkSelectStrMapViw;
        await browser.execute('arguments[0].click();', MapIcon2);
        await expect(await StoreDetailsPageObjects.mapItBtnMapVwPg).toBeDisplayed();
        await elementActions.click(StoreDetailsPageObjects.mapItBtnMapVwPg);
        await expect(await StoreDetailsPageObjects.showDirection).toBeDisplayed();
        await elementActions.click(StoreDetailsPageObjects.showDirection);
        
        
        //await driver.back();
        //await expect(await StoreDetailsPageObjects.lnkSelectStrMapViw).toBeDisplayed();
               
    }


    public async validateRedeemNowPage() {

        //Validate RedeemNow Page
        await elementActions.highlightElement(StoreDetailsPageObjects.redeemNowBtn);     
        await expect(await StoreDetailsPageObjects.redeemNowBtn).toBeDisplayed();
        
        await elementActions.highlightElement(StoreDetailsPageObjects.mapItRedeempg);     
        await expect(await StoreDetailsPageObjects.mapItRedeempg).toBeDisplayed();
        await elementActions.highlightElement(StoreDetailsPageObjects.downArrowRdmPg);     
        await expect(await StoreDetailsPageObjects.downArrowRdmPg).toBeDisplayed();
        await elementActions.highlightElement(StoreDetailsPageObjects.favrtStoreTxt);     
        await expect(await StoreDetailsPageObjects.favrtStoreTxt).toBeDisplayed();
        
    }

    public async searchByInvalidZipCode() {

        //await elementActions.highlightElement(StoreDetailsPageObjects.backBtn); 
        //await expect(await StoreDetailsPageObjects.backBtn).toBeDisplayed();
        await elementActions.highlightElement(StoreDetailsPageObjects.txtZipCodeSearch); 
        //Check for Invalid ZipCode
        await expect(await StoreDetailsPageObjects.txtZipCodeSearch).toBeDisplayed();     
        await elementActions.click(StoreDetailsPageObjects.txtZipCodeSearch);

      await elementActions.setValue(StoreDetailsPageObjects.txtZipCodeSearch, '99999');
        
    }

    public async searchByValidZipCode() {

        //await elementActions.highlightElement(StoreDetailsPageObjects.backBtn); 
        //await expect(await StoreDetailsPageObjects.backBtn).toBeDisplayed();
        //Check for Valid Zip COde
        await elementActions.highlightElement(StoreDetailsPageObjects.txtZipCodeSearch); 
        await expect(await StoreDetailsPageObjects.txtZipCodeSearch).toBeDisplayed();     
        await elementActions.click(StoreDetailsPageObjects.txtZipCodeSearch);

      await elementActions.setValue(StoreDetailsPageObjects.txtZipCodeSearch, '71601');
        
    }

    public async NavigateStoreListMapViewPage() {

        await elementActions.highlightElement(StoreDetailsPageObjects.txtZipCodeSearch); 
        await expect(await StoreDetailsPageObjects.txtZipCodeSearch).toBeDisplayed();     
        await expect(await StoreDetailsPageObjects.btnLoadMore).toBeDisplayed();
        await expect(await StoreDetailsPageObjects.lnk_StoreListMapView).toBeDisplayed();
        await elementActions.highlightElement(StoreDetailsPageObjects.lnk_StoreListMapView); 
        await elementActions.click(StoreDetailsPageObjects.lnk_StoreListMapView);
        await elementActions.highlightElement(StoreDetailsPageObjects.txtZipCodeSearch); 
        await expect(await StoreDetailsPageObjects.txtZipCodeSearch).toBeDisplayed();

        await driver.pause(3000);
        const stores = await driver.$$('//div[@role="button"]');
        console.log(`Number of stores: ${stores.length}`);

    }


    public async validatetheErrorMessageforInvalidZip() {
        const errmsg_ZipCodeInvalid = 'We are sorry but there are no participating stores in your area at this time. Please check back in the future.';
        console.log(errmsg_ZipCodeInvalid);
    
        await driver.pause(3000);
        //Validate the Actual and Expected Text 
        await elementActions.isDisplayed(StoreDetailsPageObjects.errormsg_PopupStoreList);
        const successMessage = (await StoreDetailsPageObjects.errormsg_PopupStoreList).getText();
        const actualsuccessmessage = (await successMessage);
        console.log('Generated Text is: ', actualsuccessmessage);
        console.log('Expected Text is: ', errmsg_ZipCodeInvalid);
        expect(actualsuccessmessage).toEqual(errmsg_ZipCodeInvalid);
        console.log('Success Message is Displayed Successfully');
    
        await driver.pause(3000);
        //Close the Popup
        await elementActions.highlightElement(StoreDetailsPageObjects.errmsgCloseBtn);
        await expect(await StoreDetailsPageObjects.errmsgCloseBtn).toBeDisplayed();
        await elementActions.click(StoreDetailsPageObjects.errmsgCloseBtn);
        console.log('Store List Page - Close popup button');
        
    
    }

    public async clickonmapitlink()
    {
        await expect(await StoreDetailsPageObjects.mapItRedeempg).toBeDisplayed();
        await elementActions.click(StoreDetailsPageObjects.mapItRedeempg);
        console.log('Navigation from Store list map it page')
    }

    public async navigateshowdirectionsbutton()
    {
        await expect(await StoreDetailsPageObjects.btnshowdirections).toBeDisplayed();
        await elementActions.click(StoreDetailsPageObjects.btnshowdirections);
        console.log('Navigation from show directions page')
    }


    public async select3rdStoreandValidateErrorMsg() {

        await expect(await CouponRedemptionPageObject.lnk_Store2).toBeDisplayed();
        await elementActions.click(CouponRedemptionPageObject.lnk_Store2);

        await expect(await CouponRedemptionPageObject.btn_NotNowAEM).toBeDisplayed();
        await expect(await CouponRedemptionPageObject.btn_RedeemNowAEM).toBeDisplayed();
        await elementActions.click(CouponRedemptionPageObject.btn_RedeemNowAEM);

        const errmsg_StoreCoupon = 'You must be in the store to redeem this coupon offer.';
        console.log(errmsg_StoreCoupon);
    
        await driver.pause(3000);
        //Validate the Actual and Expected Text 
        await elementActions.isDisplayed(StoreDetailsPageObjects.errormsg_PopupStoreCoupon);
        const successMessage = (await StoreDetailsPageObjects.errormsg_PopupStoreCoupon).getText();
        const actualsuccessmessage = (await successMessage);
        console.log('Generated Text is: ', actualsuccessmessage);
        console.log('Expected Text is: ', errmsg_StoreCoupon);
        expect(actualsuccessmessage).toEqual(errmsg_StoreCoupon);
        console.log('Success Message is Displayed Successfully');
    
        await driver.pause(3000);
        //Close the Popup
        await elementActions.highlightElement(StoreDetailsPageObjects.errormsg_CloseBtnStoreCoupon);
        await expect(await StoreDetailsPageObjects.errormsg_CloseBtnStoreCoupon).toBeDisplayed();
        await elementActions.click(StoreDetailsPageObjects.errormsg_CloseBtnStoreCoupon);
        console.log('Store List Page - Close popup button');
        
    
    }

}

export default new storeDetails();