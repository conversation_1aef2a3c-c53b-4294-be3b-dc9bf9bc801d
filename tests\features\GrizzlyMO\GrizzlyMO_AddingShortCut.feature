Feature: Grizzly<PERSON> PWA - Add PWA ShortCut
    As a RJR user
    I want to access and add the shortcut across different pages of PWA
    
    @CouponHomePageAddShortcut
    Scenario Outline: Validate GrizzlyMO PWA short cut adding on coupon home page
      Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        When I navigate and add <Brand> PWA link
        #Then I Add PWA link pop up
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>        

@GrizzlyMOCouponHomePageShortcut_QAstage
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizz<PERSON> Nico<PERSON> Pouches|

@GrizzlyMOCouponHomePageShortcut_Prod
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|


@StoreDetailsAddingshortcut
Scenario Outline: Validate user is able to get the <Brand> pwa notification on store details page and able to add it in the device.
Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        When I select a coupon and click on choose a store button
        Then I select a store from store list page
        When I navigate and add <Brand> PWA link
        #Then I Add PWA link pop up
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>        

@GrizzlyMOStoredetailsaddingShortcut_QAstage
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

@GrizzlyMOStoredetailsaddingShortcut_Prod
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|    


@StorelistAddingshortcut
Scenario Outline: Validate user is able to get the <Brand> pwa notification on store list page and able to add it in the device.
Given I am on the <Brand> login page at <URL>
And I set my location to "<Latitude>" and "<Longitude>"
When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
Then I click the "Understood" button
When I navigate to <Brand> offers page
Then I should be on the Coupons Home page
When I select a coupon and click on choose a store button
When I navigate and add <Brand> PWA link
When I launch the PWA App <appName> and app activity as <appActivity>
Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>

@GrizzlyMOStoreListaddingShortcut_QAstage
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

@GrizzlyMOStoreListaddingShortcut_Prod
Examples:
| Brand     |  URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|    


@StorelistMapitAddingshortcut
Scenario Outline: Validate user is able to get the <Brand> pwa notification on map it page and able to add it in the device.
Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        When I select a coupon and click on choose a store button
        Then I click on Map it link on first store
        When I navigate and add <Brand> PWA link
        #Then I Add PWA link pop up
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>        

@GrizzlyMOStoreListMapitaddingShortcut_QAstage
Examples:
| Brand    | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
|GRIZZLYMO | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

@GrizzlyMOStoreListMapitaddingShortcut_Prod
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|   


@GrizzlyMOShowdirectionsAddingshortcut
Scenario Outline: Validate user is able to get the <Brand> pwa notification on show directions page and able to add it in the device.
Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        When I select a coupon and click on choose a store button
        Then I click on Map it link on first store available
        When I click on show directions button
        When I navigate and add <Brand> PWA link
        #Then I Add PWA link pop up
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>        

@GrizzlyMOShowdirectionsaddingShortcut_QAstage
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

@GrizzlyMOShowdirectionaddingShortcut_Prod
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|    


@GrizzlyMapviewAddingshortcut
Scenario Outline: Validate user is able to get the <Brand> pwa notification on map view page and able to add it in the device.
Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        When I select a coupon and click on choose a store button
        Then I navigate to store list map view page
        When I navigate and add <Brand> PWA link
        #Then I Add PWA link pop up
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>        

@GrizzlyMOMapviewaddingShortcut_QAstage
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

@GrizzlyMOMapviewaddingShortcut_Prod
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName|
| GRIZZLYMO | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|    