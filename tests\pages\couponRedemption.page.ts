import { $, browser } from '@wdio/globals';
import Page from './Page.ts';
import elementActions from '../support/actions/element.actions.ts';
import couponRedemptionPageObject from '../pageObjects/CouponRedemption.pageObject.ts';
import LocationServicesPageObjects from '../pageObjects/LocationServices.pageObjects.ts';

class CouponRedemption extends Page {

    public async validateUserOnActiveCouponPage() {
        couponRedemptionPageObject.allCoupons.forEach(async (coupon) => {
            await expect(coupon).toBeDisplayed();
        });
    }

    public async navigateToStoreListPage() {
        await elementActions.click(await couponRedemptionPageObject.personalizedCoupon);
        await elementActions.click(await couponRedemptionPageObject.btn_ChooseStore);
    }

    public async navigateToRedeemNowPage() {
        await elementActions.click(await couponRedemptionPageObject.lnk_Store);

    }

    public async navigateToActiveCouponPage() {
        await expect(await couponRedemptionPageObject.btn_NotNowAEM).toBeDisplayed();
        await expect(await couponRedemptionPageObject.btn_RedeemNowAEM).toBeDisplayed();
        await elementActions.click(await couponRedemptionPageObject.btn_RedeemNowAEM);

    }

    public async navigateToSurveyOptions() {

        if(await elementActions.isElementPresent(couponRedemptionPageObject.iAmDone))
        {
            await elementActions.click(await couponRedemptionPageObject.iAmDone);
        }
        await elementActions.click(await couponRedemptionPageObject.iAmDone);
        if(await elementActions.isElementPresent(couponRedemptionPageObject.modalmessage))
        {
            elementActions.click(await couponRedemptionPageObject.modalmsgclosebutton);
        }
        if(await elementActions.isElementPresent(couponRedemptionPageObject.dneleave_yes))
        {
                await elementActions.click(await couponRedemptionPageObject.dneleave_yes);
        }
        else{
            await expect(await couponRedemptionPageObject.dneleave_yes_Grizzly).toBeDisplayed();

            await elementActions.click(await couponRedemptionPageObject.dneleave_yes_Grizzly);
        }
    }

    public async markStoreasFavourite() {


        await elementActions.highlightElement(couponRedemptionPageObject.favouriteStoreCheckbox);
        await elementActions.click(await couponRedemptionPageObject.favouriteStoreCheckbox);

    }

    public async validateFavouriteStoreStoreListPage() {
        await elementActions.highlightElement(couponRedemptionPageObject.storeListDownArrow);
        await expect(await couponRedemptionPageObject.storeListDownArrow).toBeDisplayed();
        await elementActions.click(await couponRedemptionPageObject.storeListDownArrow);

        await elementActions.highlightElement(couponRedemptionPageObject.goldStar);
        await expect(await couponRedemptionPageObject.goldStar).toBeDisplayed();

        await elementActions.highlightElement(couponRedemptionPageObject.lnk_Store);
        await expect(await couponRedemptionPageObject.lnk_Store).toBeDisplayed();
        await elementActions.click(await couponRedemptionPageObject.lnk_Store);

        await elementActions.highlightElement(couponRedemptionPageObject.unCheckFavrtStore);
        await expect(await couponRedemptionPageObject.unCheckFavrtStore).toBeDisplayed();
        await elementActions.click(await couponRedemptionPageObject.unCheckFavrtStore);

        await elementActions.highlightElement(couponRedemptionPageObject.storeListDownArrow);
        await expect(await couponRedemptionPageObject.storeListDownArrow).toBeDisplayed();
        await elementActions.click(await couponRedemptionPageObject.storeListDownArrow);

        await elementActions.highlightElement(couponRedemptionPageObject.greyStar);
        await expect(await couponRedemptionPageObject.greyStar).toBeDisplayed();
    }

    public async validateGrizzlyContentPage() {

        await elementActions.highlightElement(couponRedemptionPageObject.grizzly_Contentile1img);
        await elementActions.highlightElement(couponRedemptionPageObject.grizzly_Contentile2img);
        await browser.getUrl();
        const mainWindowHandle = await browser.getWindowHandle();
        await elementActions.click(couponRedemptionPageObject.grizzly_Contentile1img);
        //await driver.switchContext('NATIVE_APP');
        if (driver.isAndroid) { /* empty */ } 
        else {
            //await elementActions.clickElementIfExistsAndVisible(LocationServicesPageObjects.iOSLocationServiceAllowBtn);
            //const contexts = await driver.getContexts();
            //const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
            //console.log(contexts);
            //if (webviewContext) {
                //await driver.switchContext(webviewContext.toString());
            //}
            const contexts = await driver.getContexts();
    console.log('present context:', contexts);
    //browser.pause(3000);
    await browser.acceptAlert();

    const contexts1 = await driver.getContexts();
    console.log('present contexty:', contexts1);
 
    const newHandles = await browser.getWindowHandles();
    console.log('New Window Handles:', newHandles);
    const _handles = await browser.getWindowHandles();
    await browser.switchToWindow(newHandles[1]);

        }




        // Wait for a new window to appear
        await browser.waitUntil(
            async () => (await browser.getWindowHandles()).length > 1,
            {
                timeout: 5000,
                timeoutMsg: 'Expected a new window to open',
            });

        // Get all window handles
        const allHandles = await browser.getWindowHandles();
        const newWindowHandle = allHandles.find(handle => handle !== mainWindowHandle);

        // Switch to the new window
        if (newWindowHandle) {
            await browser.switchToWindow(newWindowHandle);

            // Do stuff in the new window

            await browser.closeWindow();
            //await browser.switchToWindow(mainWindowHandle);

        } else {
            throw new Error('New window handle not found');
        }
        const mainWindowHandle1 = await browser.getWindowHandle();
        const contexts1 = await driver.getContexts();
        console.log(contexts1);
        await elementActions.click(await couponRedemptionPageObject.grizzly_Contentile2img);
        const contexts2 = await driver.getContexts();
        console.log(contexts2);
        await driver.switchContext('NATIVE_APP');
        if (driver.isAndroid) {

        } else {
            await elementActions.clickElementIfExistsAndVisible('~Allow');
            const contexts = await driver.getContexts();
            const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
            console.log(contexts);
            if (webviewContext) {
                await driver.switchContext(webviewContext.toString());
            }

        }

        // Wait for a new window to appear
        await browser.waitUntil(
            async () => (await browser.getWindowHandles()).length > 1,
            {
                timeout: 5000,
                timeoutMsg: 'Expected a new window to open',
            });

        // Get all window handles
        const allHandles1 = await browser.getWindowHandles();
        const newWindowHandle1 = allHandles.find(handle => handle !== mainWindowHandle1);

        // Switch to the new window
        if (newWindowHandle1) {
            await browser.switchToWindow(newWindowHandle1);

            // Do stuff in the new window

            await browser.closeWindow();
            //await browser.switchToWindow(mainWindowHandle);

        } else {
            throw new Error('New window handle not found');
        }

    }
}

export default new CouponRedemption();