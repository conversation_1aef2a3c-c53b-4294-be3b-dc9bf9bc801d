import { browser } from '@wdio/globals';

export default class Page {
   /**
    * Opens a sub page of the page
    * @param path path of the sub page (e.g. /path/to/page.html)
    */
   public async opensample(path?: string) {
      await browser.url(`https://saucedemo.com${path}`);
   }

   public async open(path?: string) {
      await browser.url(path);
   }

   async handlePermissionWithContextSwitch() {
      try {
         // Get current context
         const currentContext = await browser.getContext();
         console.log('Current context:', currentContext);

         // Switch to native context to handle permission dialog
         const contexts = await browser.getContexts();
         const nativeContext = contexts.find(context => String(context).includes('NATIVE'));

         if (nativeContext) {
            await browser.switchContext(nativeContext);

            // Handle permission based on platform
            await this.handleMobileNotificationPermission();

            // Switch back to web context
            await browser.switchContext(currentContext);
         }

      } catch (error: any) {
         console.log('Context switching failed:', error.message);
      }
   }
   async handleAndroidNotificationPermission() {
      try {

         // Look for "Allow" button in the native Android permission dialog
         const allowButton = await $('//android.widget.Button[@text="Allow"]');
         if (await allowButton.isDisplayed()) {
            await allowButton.click();
            return;
         }

         // Alternative selectors for different Android versions
         const allowButtonAlt = await $('//android.widget.Button[@resource-id="com.android.permissioncontroller:id/permission_allow_button"]');
         if (await allowButtonAlt.isDisplayed()) {
            await allowButtonAlt.click();
            return;
         }

         // For newer Android versions
         const allowButtonNew = await $('//android.widget.Button[contains(@text, "Allow")]');
         if (await allowButtonNew.isDisplayed()) {
            await allowButtonNew.click();
         }

      } catch (error: any) {
         console.log('Android notification permission dialog not found or already handled', error.message());
      }
   }

   // Method 2: iOS - Using Appium commands for native permission dialog
   async handleiOSNotificationPermission() {
      try {

         // Look for "Allow" button in the native iOS permission dialog
         const allowButton = await $('//XCUIElementTypeButton[@name="Allow"]');
         if (await allowButton.isDisplayed()) {
            await allowButton.click();
            return;
         }

         // Alternative for different iOS versions
         const allowButtonAlt = await $('//XCUIElementTypeButton[contains(@name, "Allow")]');
         if (await allowButtonAlt.isDisplayed()) {
            await allowButtonAlt.click();
         }

      } catch (error: any) {
         console.log(`iOS notification permission dialog not found or already handled:${error.message}`);
      }
   }

   // Method 3: Cross-platform handler
   async handleMobileNotificationPermission() {
      const platform = browser.capabilities.platformName?.toLowerCase();

      if (platform?.includes('android')) {
         await this.handleAndroidNotificationPermission();
      } else if (platform?.includes('ios')) {
         await this.handleiOSNotificationPermission();
      }
   }
}

