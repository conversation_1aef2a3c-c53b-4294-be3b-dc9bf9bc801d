Feature: Camel PWA - Coupon Home
    As a RJR user
    I want to access and validate the coupon home page
    So that I can ensure the coupon home functionality works correctly

    @CamelValidateCouponHome
    Scenario Outline: Validate Camel coupon home page and coupon availability
    Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        #Then I Validate the Coupon Home Page
        Then I validate the <Brand> CouponHome Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
         
 @CamelCouponHome_QAstage
 Examples:
| Brand   | URL                        | Username                                     | Password  | Latitude          | Longitude          |appName                 |filename              | sheetname       | scenarioname         |
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|mobile-grizzlyMO.json | CouponHome_Page | Validate Coupon Page |


    @CamelCouponSort 
    Scenario Outline: Validate Camel coupon Sort
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        Then I Validate the Coupon Sort
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        

@CamelCouponSort_QAstage
Examples:
| Brand   | URL                        | Username                                     | Password  | Latitude          | Longitude          |appName                 |
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|


    @CamelCouponRestrictions 
    Scenario Outline: Validate the errorpopup stating that Camel coupons are not available for restricted location
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        Then I should be on the Coupons Home page
        Then I validate the error popup stating that <Brand> coupons are not available for restricted location "<location>"
        
@CamelCouponvalidateRestrictions_QAstage
Examples:
| Brand   | URL                        | Username                                      | Password  | location         | Latitude     | Longitude  |appName                 |
#| CAMEL | https://mobile-qa.camel.com/ | <EMAIL>     | Password1 | New Jersey       |40.733490     | -74.163840 | Camel |
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | Massachusetts         |42.35908      |   -71.0936 | Camel |
#| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 |New York     | 42.95875     |  -76.90899 |   Camel |

@CamelAllTypeCoupon
Scenario Outline: Validate Camel All Type coupons
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        When I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        Then I validate the "<Coupontype>" coupon
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@GrizzlyMOAllTypeCoupon_QAstage
Examples:
| Brand   | URL                        | Username                                     | Password  | Latitude          | Longitude          | Coupontype |appName                 |
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | WEEKLY     |Camel|
| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | WELCOME    |Camel|
#| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | BIRTHDAY   |Camel|
#| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | THANK      |Camel|


 @CamelEitherORCoupon
    Scenario Outline: Validate GrizzlyMO Either Or coupons
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I validate the EitherOR coupons
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
               
@CamelEitherOrCoupon_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
|CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|


 @CamelValidatCouponTerms
    Scenario Outline: Validate Camel Coupon Terms in coupon home page
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I Validate the Coupon Home Page
        Then I validate the coupon terms message
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@CamelCouponTerms_QAstage
Examples:
    | Brand   | URL                        | Username                                     | Password  | Latitude          | Longitude          |appName                 |
    | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|
