Feature: GrizzlyMO PWA - Coupon Home
    As a RJR user
    I want to access and validate the coupon home page
    So that I can ensure the coupon home functionality works correctly

    @GrizzlyMOValidateCouponHome
    Scenario Outline: Validate GrizzlyMO coupon home page and coupon availability
    Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        #Then I Validate the Coupon Home Page
        Then I validate the <Brand> CouponHome Page with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
        When I navigate to Hamburger Menu
        Then I validate the elements in Grizzly Hamburger Menu
        When I click on Grizzly Logout link
         
 @GrizzlyMOCouponHome_QAstage
 Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |filename              | sheetname       | scenarioname         |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|mobile-grizzlyMO.json | CouponHome_Page | Validate Coupon Page |


    @GrizzlyMOCouponSort 
    Scenario Outline: Validate GrizzlyMO coupon Sort
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        Then I Validate the Coupon Sort
        When I navigate to Hamburger Menu
        Then I validate the elements in Grizzly Hamburger Menu
        When I click on Grizzly Logout link
        

@GrizzlyMOCouponSort_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|


    @GrizzlyMOCouponRestrictions 
    Scenario Outline: Validate the errorpopup stating that GrizzlyMO coupons are not available for restricted location
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        #When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        Then I should be on the Coupons Home page
        Then I validate the error popup stating that GrizzlyMO coupons are not available for restricted location "<county>"
        
@GrizzlyCouponvalidateRestrictions_QAstage
Examples:
| Brand   | URL                                          | Username                              | Password  | county         | Latitude     | Longitude  |appName                 |
#| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | Saratoga County | 43.2876868  | -73.6377896 | Grizzly Nicotine Pouches |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | Saratoga County | 43.2458779  | -73.8469289 | Grizzly Nicotine Pouches |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | Saratoga County | 43.1761002  | -73.7072597 | Grizzly Nicotine Pouches |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | Saratoga County | 43.2905737  | -73.9799586 | Grizzly Nicotine Pouches |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | Saratoga County | 43.262492   | -73.6072724 | Grizzly Nicotine Pouches |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | Saratoga County | 42.8113634  | -73.7004872 | Grizzly Nicotine Pouches |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | Saratoga County | 43.0925812  | -73.6012675 | Grizzly Nicotine Pouches |
#| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | Saratoga County | 43.006933   | -73.8437197 | Grizzly Nicotine Pouches |
#| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | Saratoga County | 43.0766     | -73.7683575 | Grizzly Nicotine Pouches |



@GrizzlyMOAllTypeCoupon
Scenario Outline: Validate GrizzlyMO All Type coupons
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        When I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I should be on the Coupons Home page
        Then I validate the "<Coupontype>" coupon
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        

@GrizzlyMOAllTypeCoupon_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude          | Longitude          | Coupontype |appName                 |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | WEEKLY     |Grizzly Nicotine Pouches|
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | WELCOME    |Grizzly Nicotine Pouches|
#| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | BIRTHDAY   |Grizzly Nicotine Pouches|
#| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | THANK      |Grizzly Nicotine Pouches|


 @GrizzlyMOEitherORCoupon
    Scenario Outline: Validate GrizzlyMO Either Or coupons
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I validate the EitherOR coupons
        When I navigate to Hamburger Menu
        Then I validate the elements in Grizzly Hamburger Menu
        Then I click on <Brand> Logout link
        
@GrizzlyMOEitherOrCoupon_QAstage
Examples:
| Brand   | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
| GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|



 @GrizzlyMOValidatCouponTerms
    Scenario Outline: Validate GrizzlyMO Coupon Terms
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I Validate the Coupon Home Page
        Then I validate the coupon terms message
        When I navigate to Hamburger Menu
        Then I validate the elements in Grizzly Hamburger Menu
        Then I click on <Brand> Logout link
        

@GrizzlyMOCouponTerms_QAstage
Examples:
    | Brand   | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
    | GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|
