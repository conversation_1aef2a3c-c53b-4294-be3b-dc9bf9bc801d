@ocr-demo
Feature: OCR-based Text Clicking Demo
  As a test automation engineer
  I want to click on text elements using proper OCR with bounding boxes
  So that I can interact with elements based on their visual text content

  Background:
    Given I am on a page with text elements

  @ocr-click-basic
  Scenario: Click on text using basic OCR
    When I click on the text "Sort By:" using OCR
    Then I should see all text elements with their coordinates

  @ocr-click-case-sensitive
  Scenario: Click on text using case-sensitive OCR
    When I click on the text "WARNING" using OCR with case sensitivity enabled
    Then the text "WARNING" should be clickable via OCR

  @ocr-click-case-insensitive
  Scenario: Click on text using case-insensitive OCR
    When I click on the text "warning" using OCR with case sensitivity disabled
    Then the text "warning" should be clickable via OCR

  @ocr-search-demo
  Scenario: Search for text elements containing specific text
    When I search for text containing "OFF" using OCR
    Then I should see all text elements with their coordinates

  @ocr-full-demo
  Scenario: Demonstrate complete OCR clicking functionality
    When I demonstrate OCR clicking on "GOOD ON ANY STYLE"
    Then I should see all text elements with their coordinates

  @ocr-coupon-elements
  Scenario: Click on various coupon page elements using OCR
    When I click on the text "Sort By:" using OCR
    And I click on the text "CHOOSE A STORE" using OCR
    And I click on the text "OFF 1 CAN" using OCR
    Then I should see all text elements with their coordinates
