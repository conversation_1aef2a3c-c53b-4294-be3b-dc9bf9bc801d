@GrizzlyMOLocationServicesFunctionality
Feature: Grizzly MO PWA - Location Services Functionalities
  As an RJR user, I should be able to validate the Grizzly MOLocation Services functionality

  @GrizzlyMOAccessToBrowserAndDeviceLocation
  Scenario Outline: Validate user is able to navigate to coupon page after click on Understood button when the application has access to the browser location and device location
    Given I am on the login page for <Brand> with URL <URL>
    And I set the device location to <Latitude> and <Longitude>
    When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
    Then I click the "Understood" button
    When I navigate to <Brand> offers page
    When I navigate and add <Brand> PWA link
    When I launch the PWA App <appName> and app activity as <appActivity>
    Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
    When I navigate to <Brand> offers page
    Then I Validate the Coupon Home Page
    When I navigate to Hamburger Menu
    Then I click on Grizzly Logout link
    
    @GrizzlyMOAccessToBrowserAndDeviceLocation_QA
    Examples:
      | Brand   | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
      | GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

  @GrizzlyMOLocatonInstructionClickBackToGrizzly
  Scenario Outline: Validate the Location setting instruction screen and click backtoGrizzly
    Given I am on the login page for <Brand> with URL <URL>
    When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
    When I click the Understood button and Don't allow the Location
    Then I Validate Location Disabled screen and click continue with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    When I Validate Location Disabled screen2 with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    Then I Validate Location Disabled screen3 with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    When I Validate Location Disabled screen4 with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    Then I Validate Location Disabled screen5 with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    When I Validate Location Disabled Last Screen and Click on BackToGrizzly Button with filepath <filename> sheet name <sheetname> and scenario name <scenarioname>
    
    @LocatonInstruction_QA
    Examples:
      | Brand   | URL                                          | Username                                | Password  | Latitude          | Longitude          | filename              | sheetname     | scenarioname      |appName                 |
      | GRIZZLYMO | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | mobile-grizzlyMO.json | Location_Page | Validate Loc Page |Grizzly Nicotine Pouches|
 
    @LocatonInstruction_Prod
    Examples:
      | Brand   | URL                                    | Username                                | Password  | Latitude          | Longitude          | filename              | sheetname     | scenarioname      |appName                 |
      | GRIZZLYMO | https://www.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 | mobile-grizzlyMO.json | Location_Page | Validate Loc Page |appName                 |
 

@GrizzlyMOBackBtnLocation
  Scenario Outline: Validate the Aem Page After Location block  
    Given I am on the login page for <Brand> with URL <URL>
    And I set the device location to <Latitude> and <Longitude>
    When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
    Then I click the "Understood" button
    When I navigate to <Brand> offers page
    When I navigate and add <Brand> PWA link
    When I launch the PWA App <appName> and app activity as <appActivity>
    Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
    #When I navigate to <Brand> offers page
    When I click the Back button
    Then I validate the MobileSite Link Aem Page
    Then I validate the MobileSite Link Aem Page and click Logout


    @GrizzlyMOBackBtnLocation_QA
    Examples:
      | Brand   | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
      | GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

