import axios, { AxiosResponse } from 'axios';


export interface LoginRequest {
    env: string;
    brand: string;
    channel: string;
    username: string;
    password: string;
    pin: string;
    touch: string;
}

  export interface LoginResponse {
    user_id: string;
    User_Name: string;
    FPEnabled: string;
    PinEnabled: string;
    DivisionId: string;
    SiteCode: null | string;
    FirstName: string;
    MiddleInitial: string;
    LastName: string;
  }

export interface MobileOffersRequest {
    env: string;
    brand: string;
    channel: string;
    state: string;
    zipcode: string;
    accountno: string;
    RBDSCode?: string;
}

export interface Offer {
    CampaignId: string;
    PromotionID?: string;
    MailFileID?: string;
    OfferCode: string;
    OfferDescription: string;
    OfferType: string;
    OfferValue: string;
    OfferSeries?: string;
    OfferStartDate: string;
    OfferExpiryDate: string;
    OfferStatus: string;
    OfferWarning?: string;
    OfferContent?: string;
    OfferImageURL?: string;
    UPCCode?: string;
    RBDS?: string;
    Redeemed?: string;
    RedemptionNumber?: string;
    PropositionID?: string;
    OfferSpaceID?: string;
    Weight?: string;
    Rank?: string;
    Manufacturer?: string;
    ProdCat?: string;
    ProdType?: string;
    BrandCd?: string;
}

export interface MobileOffersResponse {
    AccountNo: string;
    Offers: Offer[];
    message?: string;
}

export interface OffersCheckResult {
    hasOffers: boolean;
    offersCount: number;
    offers?: Offer[];
    accountNo?: string;
    error?: string;
}

export interface MobileOffersConfig {
    baseUrl?: string;
    env?: string;
    brand?: string;
    channel?: string;
    state?: string;
    zipcode?: string;
    password?: string;
    timeout?: number;
}

export class MobileOffersChecker {
    private baseUrl: string;
    private defaultConfig: Required<Omit<MobileOffersConfig, 'baseUrl' | 'timeout'>>;
    private timeout: number;

    constructor(config?: MobileOffersConfig) {
        // Environment-specific base URLs
        const envUrls = {
            qa: 'https://api-m-qa.raimktg.com/qa/',  
            stage: 'https://api-m-stage.raimktg.com/qa/',  
            prod: 'https://api-m.raimktg.com/',  
        };

        const env = config?.env || process.env.TEST_ENV || 'qa';
        this.baseUrl = config?.baseUrl || envUrls[env as keyof typeof envUrls] || envUrls.qa;
        this.timeout = config?.timeout || 30000;

        this.defaultConfig = {
            env: config?.env || 'qa',
            brand: config?.brand || 'grizzly',
            channel: config?.channel || 'pwa',
            state: config?.state || 'NC',
            zipcode: config?.zipcode || '27101',
            password: config?.password || 'Password1',
        };
    }

    /**
     * Logs in a user and returns the session ID
     * @param username - The username to login with
     * @param password - Optional password override
     * @returns Promise<string> - The session ID
     */
    private async login(username: string, password?: string): Promise<string> {
        const loginPayload: LoginRequest = {
            env: this.defaultConfig.env,
            brand: this.defaultConfig.brand,
            channel: this.defaultConfig.channel,
            username: username,
            password: password || this.defaultConfig.password,
            pin: 'n',
            touch: 'n',
        };

        try {
            const response: AxiosResponse<LoginResponse> = await axios.post(
                `${this.baseUrl}SOAService/Login`,
                loginPayload,
                {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'Authorization': 'test',
                    },
                    timeout: this.timeout,
                },
            );

            if (response.status !== 200) {
                throw new Error(`Login failed with status: ${response.status}`);
            }

            // Extract Session-Id from response headers
            const sessionId = response.headers['session-id'] || response.headers['Session-Id'];

            if (!sessionId) {
                throw new Error('Session ID not found in response headers');
            }

            return sessionId;
        } catch (error: unknown) {
            if (axios.isAxiosError(error)) {
                if (error.response?.status === 401) {
                    const responseText = error.response.data;
                    if (typeof responseText === 'string') {
                        if (responseText === 'Account Locked') {
                            throw new Error('Account is locked');
                        } else if (responseText === 'USERID Does Not Exist') {
                            throw new Error('User ID does not exist');
                        } else if (responseText.includes('can\'t be empty/null')) {
                            throw new Error('Username field cannot be empty');
                        }
                    }
                }
                throw new Error(`Login failed: ${error.message}`);
            }
            throw new Error(`Login failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Gets mobile offers for a given account
     * @param sessionId - The session ID from login
     * @param accountno - The account number
     * @returns Promise<MobileOffersResponse>
     */
    private async getMobileOffers(sessionId: string, accountno: string): Promise<MobileOffersResponse> {
        const queryParams = new URLSearchParams({
            env: this.defaultConfig.env,
            brand: this.defaultConfig.brand,
            channel: this.defaultConfig.channel,
            state: this.defaultConfig.state,
            zipcode: this.defaultConfig.zipcode,
            accountno: accountno,
            RBDSCode: '',
        });

        try {
            const response: AxiosResponse<MobileOffersResponse> = await axios.get(
                `${this.baseUrl}SOAService/GetMobileOffers?${queryParams.toString()}`,
                {
                    headers: {
                        'Accept': 'application/json',
                        'Session-Id': sessionId,
                    },
                    timeout: this.timeout,
                },
            );

            if (response.status !== 200) {
                throw new Error(`Get mobile offers failed with status: ${response.status}`);
            }

            return response.data;
        } catch (error: unknown) {
            if (axios.isAxiosError(error)) {
                if (error.response?.status === 401) {
                    throw new Error('Unauthorized: No Session ID provided');
                } else if (error.response?.status === 403) {
                    throw new Error('Forbidden: Invalid Session ID');
                }
                throw new Error(`Get mobile offers failed: ${error.message}`);
            }
            throw new Error(`Get mobile offers failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Checks if an account has mobile offers
     * @param username - The username to check offers for
     * @param accountno - Optional account number (if different from username logic)
     * @param password - Optional password override
     * @returns Promise<OffersCheckResult>
     */
    public async checkAccountOffers(
        username: string,
        accountno: string,
        password?: string,
    ): Promise<OffersCheckResult> {
        try {
            // Step 1: Login to get session ID
            const sessionId = await this.login(username, password);

            // Step 3: Get mobile offers
            const offersResponse = await this.getMobileOffers(sessionId, accountno);

            // Step 4: Check if offers exist
            const hasOffers = offersResponse.Offers && offersResponse.Offers.length > 0;
            const offersCount = offersResponse.Offers ? offersResponse.Offers.length : 0;

            return {
                hasOffers,
                offersCount,
                offers: offersResponse.Offers,
                accountNo: offersResponse.AccountNo,
            };

        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            return {
                hasOffers: false,
                offersCount: 0,
                error: errorMessage,
            };
        }
    }

    /**
     * Convenience method to just check if account has offers (boolean result)
     * @param username - The username to check offers for
     * @param accountno - Optional account number
     * @param password - Optional password override
     * @returns Promise<boolean>
     */
    public async hasOffers(
        username: string,
        accountno: string,
        password?: string,
    ): Promise<boolean> {
        const result = await this.checkAccountOffers(username, accountno, password);
        return result.hasOffers;
    }

    /**
     * Get detailed offers information
     * @param username - The username to get offers for
     * @param accountno - Optional account number
     * @param password - Optional password override
     * @returns Promise<Offer[]>
     */
    public async getOffers(
        username: string,
        accountno: string,
        password?: string,
    ): Promise<Offer[]> {
        const result = await this.checkAccountOffers(username, accountno, password);
        return result.offers || [];
    }

    /**
     * Update configuration
     * @param config - New configuration values
     */
    public updateConfig(config: Partial<MobileOffersConfig>): void {
        if (config.baseUrl) this.baseUrl = config.baseUrl;
        if (config.timeout) this.timeout = config.timeout;

        // Update default config
        Object.assign(this.defaultConfig, config);
    }
}

// ============= CONVENIENCE FUNCTIONS =============

/**
 * Quick function to check if an account has offers
 * @param username - The username to check
 * @param config - Optional configuration
 * @returns Promise<boolean>
 */
export async function accountHasOffers(
    username: string,
    accountno: string,
    config?: MobileOffersConfig,
): Promise<boolean> {
    const checker = new MobileOffersChecker(config);
    return await checker.hasOffers(username, accountno);
}

/**
 * Quick function to get offers count for an account
 * @param username - The username to check
 * @param config - Optional configuration
 * @returns Promise<number>
 */
export async function getOffersCount(
    username: string,
    accountno: string,
    config?: MobileOffersConfig,
): Promise<number> {
    const checker = new MobileOffersChecker(config);
    const result = await checker.checkAccountOffers(username, accountno);
    return result.offersCount;
}

/**
 * Quick function to get full offers details for an account
 * @param username - The username to check
 * @param config - Optional configuration
 * @returns Promise<OffersCheckResult>
 */
export async function getAccountOffersDetails(
    username: string,
    accountno: string,
    config?: MobileOffersConfig,
): Promise<OffersCheckResult> {
    const checker = new MobileOffersChecker(config);
    return await checker.checkAccountOffers(username, accountno);
}

// ============= EXPORT DEFAULT =============
export default MobileOffersChecker;