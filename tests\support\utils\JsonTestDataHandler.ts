import * as fs from 'fs';
import * as path from 'path';

/**
 * Handles test data stored in JSON format for WebdriverIO tests
 */
export class JsonTestDataHandler {
    private testData: Record<string, Record<string, Record<string, any>>> = {};
    private jsonFilePath: string;
    private static instance: JsonTestDataHandler;

    /**
     * Creates a test data handler instance (or returns existing singleton)
     * @param jsonFilePath Path to the JSON file containing test data
     */
    constructor(jsonFilePath: string) {
        this.jsonFilePath = path.resolve(jsonFilePath);
        this.loadTestData();
    }

    /**
     * Gets the singleton instance of JsonTestDataHandler
     * @param jsonFilePath Path to the JSON file containing test data
     */
    static getInstance(jsonFilePath: string): JsonTestDataHandler {
        if (!JsonTestDataHandler.instance) {
            JsonTestDataHandler.instance = new JsonTestDataHandler(jsonFilePath);
        }
        return JsonTestDataHandler.instance;
    }

    /**
     * Loads the test data from JSON file
     */
    private loadTestData(): void {
        try {
            const jsonData = fs.readFileSync(this.jsonFilePath, 'utf8');
            this.testData = JSON.parse(jsonData);
        } catch (error) {
            throw new Error(`Failed to load test data from JSON: ${error}`);
        }
    }

    /**
     * Gets all available sheet names
     * @returns Array of sheet names
     */
    getSheetNames(): string[] {
        return Object.keys(this.testData);
    }

    /**
     * Gets all test case names for a specific sheet
     * @param sheetName Name of the sheet
     * @returns Array of test case names
     */
    getTestCaseNames(sheetName: string): string[] {
        this.validateSheetName(sheetName);
        return Object.keys(this.testData[sheetName]);
    }

    /**
     * Validates that a sheet name exists
     * @param sheetName Name of the sheet to validate
     */
    private validateSheetName(sheetName: string): void {
        if (!this.testData[sheetName]) {
            throw new Error(`Sheet '${sheetName}' not found in test data`);
        }
    }

    /**
     * Validates that a test case exists in a sheet
     * @param sheetName Name of the sheet
     * @param testCaseName Name of the test case
     */
    private validateTestCase(sheetName: string, testCaseName: string): void {
        this.validateSheetName(sheetName);

        if (!this.testData[sheetName][testCaseName]) {
            throw new Error(`Test case '${testCaseName}' not found in sheet '${sheetName}'`);
        }
    }

    /**
     * Gets all data for a specific test case
     * @param sheetName Name of the sheet
     * @param testCaseName Name of the test case
     * @returns All data for the test case
     */
    getTestCaseData(sheetName: string, testCaseName: string): Record<string, any> {
        this.validateTestCase(sheetName, testCaseName);
        return { ...this.testData[sheetName][testCaseName] };
    }

    /**
     * Gets a specific value for a test case
     * @param sheetName Name of the sheet
     * @param testCaseName Name of the test case
     * @param columnName Name of the column
     * @returns The cell value
     */
    getCellValue(sheetName: string, testCaseName: string, columnName: string): any {
        this.validateTestCase(sheetName, testCaseName);

        if (!(columnName in this.testData[sheetName][testCaseName])) {
            throw new Error(`Column '${columnName}' not found for test case '${testCaseName}' in sheet '${sheetName}'`);
        }

        return this.testData[sheetName][testCaseName][columnName];
    }
}