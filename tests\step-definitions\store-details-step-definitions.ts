import { When, Then } from '@wdio/cucumber-framework';
import logger from '../support/utils/logger.util.ts';
import storeDetailsPage from '../pages/storeDetails.page.ts';


Then(/^I click on loadmore button and validate the list$/, async function () {
    await storeDetailsPage.clickonLoadMoreandValidateStoreList();
    console.log('Click LoadMore Button and Validate the Store List');
    await logger.info('Click LoadMore Button and Validate the Store List');
});

Then(/^I validate Search By zip with invalid zip code$/, async function () {
    await storeDetailsPage.searchByInvalidZipCode();
    console.log('Validate Serach by Invalid Zip code');
    await logger.info('Validate Serach by Invalid Zip code');
});

Then(/^I validate Search By zip with valid zip code$/, async function () {
    await storeDetailsPage.searchByValidZipCode();
    console.log('Validate Serach by valid Zip code');
    await logger.info('Validate Serach by valid Zip code');
});

Then(/^I click on Map it link on first store available$/, async function () {
    await storeDetailsPage.clickonmapitlink();
    console.log('Validate map it link on store list');
    await logger.info('Validate map it link on store list');
});

When(/^I click on show directions button$/, async function () {
    await storeDetailsPage.navigateshowdirectionsbutton();
    console.log('Validate the navigation of show direction button');
    await logger.info('Validate the navigation of show direction button');
});


Then(/^I validate the error message for invalid zipcode$/, async function () {
    await storeDetailsPage.validatetheErrorMessageforInvalidZip();
    console.log('Validate the error message for Invalid Zip code');
    await logger.info('Validate the error message for Invalid Zip code');
});

Then(/^I navigate to store list map view page$/, async function () {
    await storeDetailsPage.NavigateStoreListMapViewPage();
    console.log('Validate the Store List Map View Page');
    await logger.info('Validate the Store List Map View Page');
});

When(/^I select a store from map view$/, async function () {
    await storeDetailsPage.selectStoreFromMapView();
    console.log('Select a Store from Map View ');
    await logger.info('Select a Store from Map View ');
});

Then(/^I validate the user is on Redeem now page$/, async function () {
    await storeDetailsPage.validateRedeemNowPage();
    console.log('Validate User is on Redeem Now Page');
    await logger.info('Validate User is on Redeem Now Page');
});

Then(/^I validate the map view Page$/, async function () {
    await storeDetailsPage.validateMapViewPage();
    console.log('Validate User is on Map view Page');
    await logger.info('Validate User is on Map View Page');
});

Then(/^I select 3rd store from store list page and validate the error message$/, async function () {
    await storeDetailsPage.select3rdStoreandValidateErrorMsg();
    console.log('Select 3rd store and validate the error message');
    await logger.info('Select 3rd store and validate the error message');
});