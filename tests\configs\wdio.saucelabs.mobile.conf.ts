import type { Options } from '@wdio/types';
import { config as sauceSharedConfig } from './wdio.saucelabs.shared.conf.ts';

const build = `WebdriverIO - Cucumber - Demo - ${new Date().getTime()}`;

export const config: Options.Testrunner = {
  ...sauceSharedConfig,
  automationProtocol: 'webdriver',
  capabilities: [
    {
      // iPhone 15 Pro Max - Latest real iOS device
      'appium:deviceName': 'iPhone.*',
      'appium:automationName': 'XCUITest',
      'appium:platformVersion': '18',
      'appium:autoAcceptAlerts': true,
      'appium:autoDismissAlerts': true,
      'appium:autoGrantPermissions': true,
      browserName: 'Safari',
      platformName: 'iOS',
      'sauce:options': {
        appiumVersion: 'latest', // Latest stable Appium version
        build,
      },

      webSocketUrl: false,
    },
    // {
    //   // iPhone 15 - Another latest real iOS device
    //   'appium:deviceName': 'iPhone 15',
    //   'appium:automationName': 'XCUITest',
    //   browserName: 'Safari',
    //   platformName: 'iOS',
    //   'sauce:options': {
    //     appiumVersion: 'latest',
    //     build,
    //   },
    // },
    //  {
    //   // Samsung Galaxy S24 Ultra - Latest real Android device

    //   'appium:deviceName': 'Samsung.*',
    //   'appium:automationName': 'UiAutomator2',
    //   'appium:platformVersion': '14',
    //   browserName: 'Chrome',
    //   platformName: 'Android',
    //   'sauce:options': {
    //     appiumVersion: 'latest',
    //     build,
    //     extendedDebugging: false,
    //     capturePerformance: false,
    //   },
    //    // Chrome-specific capabilities at the root level
    //    'goog:chromeOptions': {
    //     args: [
    //         '--start-maximized',
    //         '--disable-notifications',
    //         '--disable-popup-blocking'
    //     ]
    // },
    //   webSocketUrl: false
    // },
    // {
    //   // Google Pixel 8 Pro - Latest real Android device
    //   "appium:deviceName": "Google.*",
    //   'appium:platformVersion': '15',
    //   "appium:automationName": "UiAutomator2",
    //   browserName: "Chrome",
    //   platformName: "Android",
    //   "sauce:options": {
    //     appiumVersion: "latest",
    //     build,
    //   },
    //   webSocketUrl: false,
    //   unhandledPromptBehavior:'dismiss'
    // },
  ],

  // Add these important configurations
  services: [
    [
      'sauce',
      {
        region: 'us-west-1',
      },
    ],
  ],
  maxInstances: 4,
  waitforTimeout: 30000,
  connectionRetryTimeout: 120000,
  connectionRetryCount: 3,
  framework: 'cucumber',
  specFileRetries: 0,
  specFileRetriesDelay: 0,
  specFileRetriesDeferred: false,

};
