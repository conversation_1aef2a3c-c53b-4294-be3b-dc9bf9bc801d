import elementActions from "../actions/element.actions.ts";
import { IWaitOptions } from "../types/action.types.ts";
import logger from '../utils/logger.util.ts';

type AssertionFunction<T> = (...args: unknown[]) => Promise<T>;


export class AssertionHelper {

  private readonly DEFAULT_TIMEOUT = 10000;
    private readonly DEFAULT_INTERVAL = 500;

    private createWaitOptions(options?: Partial<IWaitOptions>, timeoutMsg?: string): IWaitOptions {
      return {
          timeout: options?.timeout || this.DEFAULT_TIMEOUT,
          interval: options?.interval || this.DEFAULT_INTERVAL,
          timeoutMsg: options?.timeoutMsg || timeoutMsg || `Operation timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
          error: options?.error,
          reverse: options?.reverse || false,
      };
  }

  private async highlightOrScroll(element: ChainablePromiseElement): Promise<void> {
    // Default scroll options for consistency
    const scrollOptions = { block: 'center', inline: 'center', behavior: 'smooth' as const };

    // Check if highlighting is enabled via the environment variable
    const isHighlightEnabled = browser.highlightElements === true;

    if (isHighlightEnabled) {
        try {
            // Attempt to highlight the element
            await element.highlight();
            // If highlighting succeeds, we're done
            return;
        } catch (error) {
            // Log the error but continue with scrollIntoView as fallback
            logger.info('Element highlighting failed, falling back to scrollIntoView', { error });
        }
    }

    // Either highlighting is disabled or it failed, so use scrollIntoView as fallback
    try {
        await element.scrollIntoView(scrollOptions);
    } catch (error) {
        // Log but don't throw - scrolling is a best-effort operation
        logger.info('Failed to scroll element into view', { error });
    }
}

/**
 * Wrapper to standardize error handling and logging for assertion methods
 */
private async assertWrapper<T>(
    fn: AssertionFunction<T>,
    successMessage: string,
    errorMessage: string,
    ...args: unknown[]
): Promise<T> {
    try {
        const result = await fn(...args);
        logger.info(`Assertion passed: ${successMessage}`);
        return result;
    } catch (error) {
        logger.error(`${errorMessage}`, { error });
        throw error;
    }
}

/**
 * Helper to wait for an element to meet a condition
 */
private async waitForElementCondition(
    element: ChainablePromiseElement,
    condition: () => Promise<boolean>,
    waitOptions: IWaitOptions,
    message: string,
): Promise<void> {
    await element.waitForDisplayed(waitOptions);
    await this.highlightOrScroll(element);

    await browser.waitUntil(condition, {
        timeout: waitOptions.timeout,
        interval: waitOptions.interval,
        timeoutMsg: message,
    });
}

    async mssgcomparision(element: ChainablePromiseElement, mssg: string) {
        try {
          await elementActions.waitForDisplayed(element);
          const successMessage = (element).getText();
          const actualsuccessmessage = (await successMessage).trim();
          console.log('Generated Text is: ', actualsuccessmessage);
          console.log('Expected Text is: ', mssg);
          expect(actualsuccessmessage).toEqual(mssg.trim());
          console.log('Validated Message Successfully');
        } catch (error) {
          logger.error('Failed to Validate Message', { error });
          
          throw error;
        }
 
       
      }

      async assertElementDisplayed(
        element: ChainablePromiseElement,
        options?: IWaitOptions,
        message?: string,
    ): Promise<void> {
        return this.assertWrapper(
            async () => {
                const waitOptions = this.createWaitOptions(
                    options,
                    `Element should be displayed but timed out after ${options?.timeout || this.DEFAULT_TIMEOUT}ms`,
                );
 
                await element.waitForDisplayed(waitOptions);
                await this.highlightOrScroll(element);
 
            },
            'Element is displayed',
            'Failed to assert element is displayed',
            element, options, message,
        );
    }
    async sgwTextcomparision(actualsuccessmessage: string, msg: string) {
        try {
       
          console.log('Generated Text is: ', actualsuccessmessage);
          console.log('Expected Text is: ', msg);
          expect(actualsuccessmessage).toEqual(msg.trim());
          console.log('Validated Message Successfully');
        } catch (error) {
          logger.error('Failed to Validate Message', { error });
          throw error;
        }
 
       
      }

}

export default new AssertionHelper();