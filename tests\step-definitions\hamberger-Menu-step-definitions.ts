import { When, Then } from '@wdio/cucumber-framework';
import logger from '../support/utils/logger.util.ts';
import HambergerMenuPage from '../pages/HambergerMenu.page.ts';
import locationServicePage from '../pages/locationService.page.ts';


When(/^I navigate to the duplicate Camel Offers page$/, async function () {
    await locationServicePage.navigateToOffersPage();
});


When(/^I navigate to Hamburger Menu$/, async function () {
    await HambergerMenuPage.navigateToGrizzlyHambergerMenu();
    console.log('Clicked on Ham Menu option');
    await logger.info('Clicked on Ham Menu option');
});

Then(/^I validate the elements in Grizzly Hamburger Menu$/, async function () {
    await HambergerMenuPage.validateElementGrizzlyHambergerMenu();
    console.log('Verified all Elements in Ham Menu option');
    await logger.info('Verified all Elements in Ham Menu option');
});

When(/^I click on Grizzly coupons link$/, async function () {
    await HambergerMenuPage.verifyClickonGrizzlyCouponLink();
    console.log('Clicked on Coupons from Ham Menu option');
    await logger.info('Clicked on Coupons from Ham Menu option');
});

Then(/^I click on (.*) Logout link$/, async function (brand:string) {
    await HambergerMenuPage.verifyClickonGrizzlyLogoutLink(brand);
    console.log("The user will click on brand log out link")
});

When(/^I click on Grizzly MobileSite link$/, async function () {
    await HambergerMenuPage.verifyClickonGrizzlyMobileSiteLink();
});

Then(/^I validate the MobileSite Link Aem Page$/, async function () {
    await HambergerMenuPage.validateMobileSiteLinkAEMPage();
    console.log('Verified req. Elements in AEM Page');
    await logger.info('Verified req. Elements in AEM Page');
});

Then(/^I validate the MobileSite Link Aem Page and click Logout$/, async function () {
    await HambergerMenuPage.validateMobileSiteLinkAEMPageandLogout();
    console.log('Verified req. Elements in AEM Page and Logout');
    await logger.info('Verified req. Elements in AEM Page and Logout');

    
});

Then(/^I click HambergerMenu Close Button$/, async function () {
    await HambergerMenuPage.clickCloseBtnHmMENU();
    console.log('Verified all Elements in Ham Menu option');
    await logger.info('Verified all Elements in Ham Menu option');
});