# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- master

pool:
  vmImage: ubuntu-latest

steps:
- task: NodeTool@0
  inputs:
    versionSpec: '21.x'
  displayName: 'Install Node.js'

- script: |
    npm install
  displayName: 'npm install and build'

  # Run WebdriverIO Tests
- script: npm run test-camel-native
  displayName: 'Run Camel Native Tests'
  env:
    SAUCE_USERNAME: $(SAUCE_USERNAME)  # Use the secret variable
    SAUCE_ACCESS_KEY: $(SAUCE_ACCESS_KEY)  # Use the secret variable