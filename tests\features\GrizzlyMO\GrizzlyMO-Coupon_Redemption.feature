Feature: GrizzlyMO PWA - Coupon Redemption
    As a RJR user
    I want to access and validate the coupon Redeem page
    So that I can ensure the coupon functionality works correctly

    @GrizzlyMOCouponRedemption 
    Scenario Outline: Validate GrizzlyMO coupon Redemption
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I select a store from store list page
        When I click on the Redeem Now button
        Then I click on the I'm Done button

    @GrizzlyCouponRedemption_QAstage
    Examples:
    | Brand   | URL                                          | Username                                   | Password  | Latitude | Longitude |appName                 |
    | GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|


  @GrizzlyMOCouponRedemptionMapView 
    Scenario Outline: Validate GrizzlyMO coupon Redemption from Map View 
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I navigate to store list map view page
        When I select a store from map view
        Then I validate the user is on Redeem now page
        When I click on the Redeem Now button
        Then I click on the I'm Done button

    @CouponRedemptionfromMapView_QAstage
    Examples:
    | Brand   | URL                                          | Username                                   | Password  | Latitude | Longitude |appName                 |
    | GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|


    @GrizzlyMOContentPage 
    Scenario Outline: Validate GrizzlyMO content tiles
        Given I am on the <Brand> login page at <URL>
        And   I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I select a store from store list page
        When I click on the Redeem Now button
        Then I click on the I'm Done button
        Then I Validate the <Brand> Content Page
        
    @ContentPage_QAstage
    Examples:
    | Brand   | URL                                          | Username                            | Password  | Latitude | Longitude |appName                 |
    | GRIZZLY | https://mobile-qa.grizzlynicotinepouches.com |<EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|


@GrizzlyMOFavouriteStore 
Scenario Outline: Validate GrizzlyMO Favourite Store
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I select a store from store list page
        When I mark the store as favorite
        Then I navigate to Store List and validate the favoritestore
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
@GrizzlyMOFavouriteStore_QAstage
Examples:
| Brand   | URL                                         | Username                                | Password  | Latitude | Longitude |appName                 |
| GRIZZLY |https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 42.7098  | -71.4437  |Grizzly Nicotine Pouches|

