@GrizzlyMOLoginFunctionality
Feature: Camel PWA - Login Functionalities
  As an RJR user, I should be able to validate the Camel PWA Login page functionality

  @CamelInvalidLoginHome
  Scenario Outline: Validate error message on invalid login credentials
  Given I am on the <Brand> login page at <URL>
    And I set my location to "<Latitude>" and "<Longitude>"
    When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
    Then I click the "Understood" button
    When I navigate to the <Brand> offers page
    And I navigate and add <Brand> PWA link
    And I launch the PWA App "<appName>" and app activity as "<appActivity>"
    When I attempt to login with invalid credentials username "<InvalidUsername>" and password "<Password>"
    Then I should see an error message indicating invalid login credentials

    @CamelInvalidLoginHome_QA
    Examples:
      | Brand | URL                          | InvalidUsername              |Username                              | Password  | Latitude          | Longitude          | appName |
      | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> |<EMAIL>| Password1 | 36.09947034604555 | -80.24355602883584 | Camel   |

  @CamelUsernameAfterLogout
  Scenario Outline: Validate username persistence with Remember Me checkbox after logout
    Given I am on the login page for <Brand> with URL <URL>
    And I set the device location to <Latitude> and <Longitude>
    When I attempt to login Remember me Checkbox with valid credentials username <Username> and password <Password>
    Then I click the "Understood" button
    When I navigate to Hamburger Menu
    Then I click on <Brand> Logout link
    Then I should see my <Username> persisted in the login field

    @CamelUsernameAfterLogout_QA
    Examples:
      | Brand | URL                          | Username                         | Password  | Latitude          | Longitude          |
      | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |

  @CamelLogin128
  Scenario Outline: Validate login functionality with 128 character username
    Given I am on the login page for <Brand> with URL <URL>
    And I set the device location to <Latitude> and <Longitude>
    When I login with 128 character username <Username> and password <Password>
    Then I click the "Understood" button
    When I navigate to Hamburger Menu
    Then I click on <Brand> Logout link


    @GrizzlyMOLogin128_QA
    Examples:
      | Brand | URL                          | Username                                                                                                                        | Password  | Latitude          | Longitude          |
      | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |

  @CamelAirplaneModeOn
  Scenario Outline: Validate login behavior with Airplane mode enabled
    Given I am on the login page for <Brand> with URL <URL>
    And I set the device location to <Latitude> and <Longitude>
    When I login with username <Username> and password <Password>
    And I enable the Airplane mode
    Then I should see an appropriate connectivity error message

    @GrizzlyMOAirplaneModeOn_QA
    Examples:
      | Brand | URL                          | Username                     | Password  | Latitude          | Longitude          |
      | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |


