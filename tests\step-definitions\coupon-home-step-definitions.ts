import { Given, When, Then } from '@wdio/cucumber-framework';
import logger from '../support/utils/logger.util.ts';
import LoginPageObjects from '../pageObjects/Login.pageObjects.ts';
import couponHomePage from '../pages/couponHome.page.ts';
import locationServicePage from '../pages/locationService.page.ts';
import loginPage from '../pages/login.page.ts';
import AltHomePage from '../pages/AltHome.page.ts';
import LocationServicesPageObjects from '../pageObjects/LocationServices.pageObjects.ts';
import CouponRedemptionPageObject from '../pageObjects/CouponRedemption.pageObject.ts';
import AltHomePageObjects from '../pageObjects/AltHome.pageObjects.ts';


Given(/^I am on the (.*) login page at (.*)$/, async function (brand: string, url: string) {
    // [Given] Sets up the initial state of the system.
    await logger.info(`Step: Navigating to login page of ${brand}`);
    await loginPage.open(url);
    await driver.waitUntil(async () => {
        return await (await LoginPageObjects.usernameInput).isExisting();
    }, { timeout: 90000 });
});

When(/^I set my location to "([^"]*)" and "([^"]*)"$/, async function (latitude: string, longitude: string) {
    // [Given] Sets up the initial state of the system.
    await logger.info('Step: Set the device location');
    const lat = parseFloat(latitude);
    const lon = parseFloat(longitude);
    await loginPage.setLocation(lat, lon);
});

When(/^I log in with valid username "([^"]*)" and password "([^"]*)" for "([^"]*)"$/, async function (username: string, password: string, brand: string) {
    // [When] Describes the action or event that triggers the scenario.
    await logger.info(`Step: Login to the ${brand} PWA using valid email and password`);
    await loginPage.login(username, password, brand);
});

When(/^I navigate to the Camel Offers page$/, async function () {
    await locationServicePage.navigateToOffersPage();
});

Then(/^I navigate to the Grizzly Offers page$/, async function () {
    await locationServicePage.navigateToGrizzlyOffersPage();
});

Then(/^I click the "([^"]*)" button$/, async function (buttonText: string) {
    if (buttonText === 'Redeem Now') {
        await locationServicePage.navigateToLocationServicesPage();
    };
    if (buttonText === 'Understood') {
        await locationServicePage.navigateToActiveCouponPage();
    }
});

When(/^I should be on the Coupons Home page$/, async function () {
    await couponHomePage.validateUserOnActiveCouponPage();
    //await expect(await CouponHomePageObjects.allCpns).toBeDisplayed();
});

Then(/^I validate the (.*) CouponHome Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (Brand :string, filepath :string, sheetname :string, scenarioname :string) {
    await couponHomePage.validateallCouponsandTextsOnCouponHomePage(Brand,filepath,sheetname,scenarioname);
    console.log('Validate all coupons and texts on the Coupons Home Page');
    await logger.info('Validate all coupons and texts in the Coupons Home Page');
});

Then(/^I Validate the Coupon Home Page$/, async function () {
    await couponHomePage.validateUserOnCouponHomePage();
});

Then(/^I validate the error popup stating that (.*) coupons are not available for restricted location "([^"]*)"$/, async function (brand:string, location: string) {
    // [When] Describes the action or event that triggers the scenario.
    await couponHomePage.validateerrorMsgPopupforRestrictedLoc(brand, location);
    await logger.info('Validate the errorpopup stating that GrizzlyMO coupons are not available for restricted location');
});

Then(/^I validate the "([^"]*)" coupon$/, async function (coupontype: string) {

    await couponHomePage.validateWeeklyCoupon(coupontype);
    await logger.info('Validate the Weekly coupon');
});

Then(/^I validate the EitherOR coupons$/, async function () {

    await couponHomePage.validateEitherORCoupons();
    await logger.info('Validate the EitherOR coupons');
});

Then(/^I validate the coupon terms message$/, async function () {

    await couponHomePage.validateCouponHomePageTerms();
    await logger.info('Validate the coupon Terms at Coupon Home Page');
});

Then(/^I Validate the Coupon Sort$/, async function () {

    await couponHomePage.validateCouponSort();
    await logger.info('Validate the coupon Sorted by ExpiryDate at Coupon Home Page');
});

When(/^I navigate and add (.*) PWA link$/, async function(Brand: string)
{
   //await couponHomePage.addingpwalink(Brand);
   await loginPage.installPWAApp();
   await logger.info('Validating the adding home screen functionality for the brand')
});

Then(/^I navigate to (.*) offers page$/, async function(brand: string)
{
   await couponHomePage.navigatetoofferspage(brand);
   await logger.info('Validating the navigating to offers page')
   await expect(await CouponRedemptionPageObject.personalizedCoupon).toBeDisplayed();
   
   
});

When(/^I validate the Alt Home Page for the brand (.*)$/, async function(brand: string)
{
   await AltHomePage.validateAltHomePage(brand);
   await logger.info('Validating the Alt Home page')
   //await expect(await AltHomePageObjects.navBarLogo).toBeDisplayed();
});
When(/^I launch the PWA App (.*) and app activity as (.*)$/, async function(appName: string, appActivity: string) {
    //await couponHomePage.launchingAppName(appName, appActivity);
    await loginPage.launchPWA(appName);
    await logger.info('Validating the launch');
});