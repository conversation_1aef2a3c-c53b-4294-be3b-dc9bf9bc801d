import { Given, When, Then } from '@wdio/cucumber-framework';
import ocrClickUtil from '../support/utils/ocrClickUtil.ts';
import ocrClickDemo from '../support/utils/ocrClickDemo.ts';
import logger from '../support/utils/logger.util.ts';

/**
 * Step definitions for OCR-based text clicking
 */

Given(/^I am on a page with text elements$/, async () => {
  // This step assumes you're already on a page with text
  // You can add navigation logic here if needed
  logger.info('Ready to perform OCR-based text operations');
});

When(/^I click on the text "([^"]*)" using OCR$/, async (textToFind: string) => {
  logger.info(`Attempting to click on text: "${textToFind}" using proper OCR`);
  
  const success = await ocrClickUtil.clickOnText(textToFind);
  
  if (!success) {
    throw new Error(`Failed to find and click on text: "${textToFind}"`);
  }
  
  logger.info(`Successfully clicked on text: "${textToFind}"`);
});

When(/^I click on the text "([^"]*)" using OCR with case sensitivity (enabled|disabled)$/, async (textToFind: string, caseSensitivity: string) => {
  const caseSensitive = caseSensitivity === 'enabled';
  logger.info(`Attempting to click on text: "${textToFind}" using OCR (case sensitive: ${caseSensitive})`);
  
  const success = await ocrClickUtil.clickOnText(textToFind, 3, 1000, caseSensitive);
  
  if (!success) {
    throw new Error(`Failed to find and click on text: "${textToFind}"`);
  }
  
  logger.info(`Successfully clicked on text: "${textToFind}"`);
});

When(/^I search for text containing "([^"]*)" using OCR$/, async (searchText: string) => {
  logger.info(`Searching for text containing: "${searchText}"`);
  await ocrClickDemo.findTextElements(searchText);
});

When(/^I demonstrate OCR clicking on "([^"]*)"$/, async (textToFind: string) => {
  logger.info(`Running OCR click demonstration for: "${textToFind}"`);
  await ocrClickDemo.demonstrateOcrClick(textToFind);
});

Then(/^I should see all text elements with their coordinates$/, async () => {
  logger.info('Getting all text elements with bounding boxes...');
  
  const allTexts = await ocrClickUtil.getAllTextWithBoundingBoxes();
  
  logger.info(`Found ${allTexts.length} text elements on screen:`);
  allTexts.forEach((textElement, index) => {
    logger.info(`${index + 1}. "${textElement.text}" at (${textElement.boundingBox.x}, ${textElement.boundingBox.y}) - ${textElement.boundingBox.width}x${textElement.boundingBox.height}`);
  });
  
  // Verify we found at least some text
  if (allTexts.length === 0) {
    throw new Error('No text elements found on the screen');
  }
});

Then(/^the text "([^"]*)" should be clickable via OCR$/, async (textToFind: string) => {
  logger.info(`Verifying that text "${textToFind}" is clickable via OCR`);
  
  // Get all text elements to check if the text exists
  const allTexts = await ocrClickUtil.getAllTextWithBoundingBoxes();
  const foundText = allTexts.find(textElement => 
    textElement.text.toLowerCase().includes(textToFind.toLowerCase())
  );
  
  if (!foundText) {
    throw new Error(`Text "${textToFind}" not found on the screen`);
  }
  
  logger.info(`Text "${textToFind}" found at coordinates (${foundText.boundingBox.x}, ${foundText.boundingBox.y})`);
});
