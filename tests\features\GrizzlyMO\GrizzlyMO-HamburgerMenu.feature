Feature: GrizzlyMO PWA - Hamburger Menu
    As a RJR user
    I want to access and validate the Hamberger Menu
    So that I can ensure the Hmberger Menu Links works correctly

    @GrizzlyMOHamburgerMenu 
    Scenario Outline: Validate GrizzlyMO Coupons in HamburgerMenu
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
       Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I navigate to Hamburger Menu
        Then I validate the elements in Grizzly Hamburger Menu
        When I click on Grizzly coupons link
        Then I Validate the Coupon Home Page
        When I navigate to Hamburger Menu
        Then I validate the elements in Grizzly Hamburger Menu
        When I click on Grizzly Logout link
        
@GrizzlyMOCouponsHamburgerMenu_QAstage
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
| GRIZZLYMO | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

@GrizzlyMOCouponsHamburgerMenu_Prod
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
| GRIZZLYMO | https://www.grizzlynicotinepouches.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|


@GrizzlyMOLogoutHamburgerMenu 
    Scenario Outline: Validate GrizzlyMO Logout from HamburgerMenu
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I Validate the Coupon Home Page
        When I navigate to Hamburger Menu
        Then I validate the elements in Grizzly Hamburger Menu
        When I click on Grizzly Logout link
        
    @GrizzlyMOLogoutHamburgerMenu_QAstage
    Examples:
    | Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
    | GRIZZLYMO | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

    @GrizzlyMOLogoutHamburgerMenu_Prod
    Examples:
    | Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
    | GRIZZLYMO | https://www.grizzlynicotinepouches.com/      | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

@GrizzlyMOMobileSiteHamburgerMenu 
    Scenario Outline: Validate GrizzlyMO Mobile Site from HamburgerMenu
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I navigate to Hamburger Menu
        Then I validate the elements in Grizzly Hamburger Menu
        When I click on Grizzly MobileSite link
            
@GrizzlyMOMobileSiteHamburgerMenu_QAstage
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
| GRIZZLYMO | https://mobile-qa.grizzlynicotinepouches.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|

@GrizzlyMOMobileSiteHamburgerMenu_Prod
Examples:
| Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
| GRIZZLYMO | https://www.grizzlynicotinepouches.com/      | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Grizzly Nicotine Pouches|