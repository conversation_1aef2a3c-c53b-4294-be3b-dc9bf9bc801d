import { browser } from '@wdio/globals';
import logger from '../utils/logger.util.ts';
import {
  IGestureOptions,
  SwipeDirection,
  ScrollDirection,
  IElementWaitState,
} from '../types/action.types.ts';

export class MobileActions {
  /**
   * Performs a swipe gesture
   * @param direction - Direction to swipe: 'up', 'down', 'left', 'right'
   * @param options - Gesture options like percentage and duration
   */
  async swipe(
    direction: 'up' | 'down' | 'left' | 'right',
    options?: IGestureOptions,
  ): Promise<void> {
    try {
      const { width, height } = await browser.getWindowSize();
      const percentageX = options?.percentageX || 50;
      const percentageY = options?.percentageY || 50;
      const duration = options?.duration || 500;

      let startX: number, startY: number, endX: number, endY: number;

      switch (direction) {
        case 'up':
          startX = width * (percentageX / 100);
          startY = height * 0.8;
          endX = width * (percentageX / 100);
          endY = height * 0.2;
          break;
        case 'down':
          startX = width * (percentageX / 100);
          startY = height * 0.2;
          endX = width * (percentageX / 100);
          endY = height * 0.8;
          break;
        case 'left':
          startX = width * 0.8;
          startY = height * (percentageY / 100);
          endX = width * 0.2;
          endY = height * (percentageY / 100);
          break;
        case 'right':
          startX = width * 0.2;
          startY = height * (percentageY / 100);
          endX = width * 0.8;
          endY = height * (percentageY / 100);
          break;
      }

      await browser.touchPerform([
        {
          action: 'press',
          options: { x: startX, y: startY },
        },
        {
          action: 'wait',
          options: { ms: duration },
        },
        {
          action: 'moveTo',
          options: { x: endX, y: endY },
        },
        {
          action: 'release',
        },
      ]);

      logger.info(`Performed ${direction} swipe`);
    } catch (error) {
      logger.error(`Swipe ${direction} failed: ${error}`);
      throw error;
    }
  }

  /**
   * Performs a tap gesture
   * @param selector - Element to tap
   * @param options - Gesture options
   */
  async tap(selector: string, options?: IGestureOptions): Promise<void> {
    try {
      logger.info(`Tapping element: ${selector}`);
      const element = await $(selector);

      // Use new wait utility instead of simple waitForDisplayed
      const isInteractable = await this.waitForNativeElement(selector, options);
      if (!isInteractable) {
        throw new Error('Element not interactable after wait period');
      }

      await element.touchAction('tap');
    } catch (error) {
      logger.error(`Tap failed on ${selector}: ${error}`);
      throw error;
    }
  }

  /**
   * Performs a long press gesture
   * @param selector - Element to long press
   * @param duration - Duration in milliseconds
   */
  async longPress(selector: string, duration: number = 1000): Promise<void> {
    try {
      logger.info(`Long pressing element: ${selector}`);
      const element = await $(selector);

      // Use new wait utility
      const isInteractable = await this.waitForNativeElement(selector, {
        timeout: 10000,
        timeoutMsg: `Element ${selector} not interactable for long press`,
      });

      if (!isInteractable) {
        throw new Error(
          'Element not interactable for long press after wait period',
        );
      }

      //await element.longPress({ duration: duration });
      //await element.longPressKeyCode
    } catch (error) {
      logger.error(`Long press failed on ${selector}: ${error}`);
      throw error;
    }
  }

  /**
   * Scrolls to a specific element
   * @param selector - Element to scroll to
   * @param direction - Scroll direction
   */
  async scrollToElement(
    selector: string,
    direction: 'up' | 'down' = 'down',
  ): Promise<void> {
    try {
      logger.info(`Scrolling ${direction} to element: ${selector}`);
      const element = await $(selector);
      await element.scrollIntoView();
    } catch (error) {
      logger.error(`Scroll to element ${selector} failed: ${error}`);
      throw error;
    }
  }

  async scroll(
    direction: ScrollDirection,
    options: IGestureOptions = { percentageX: 50 },
  ): Promise<void> {
    await this.swipe(direction as SwipeDirection, options);
  }

  async hideKeyboard(): Promise<void> {
    try {
      if (driver.isIOS) {
        const doneButton = await $('~Done');
        if (await doneButton.isDisplayed()) {
          await doneButton.click();
        }
      } else {
        await driver.hideKeyboard();
      }
    } catch (error) {
      console.log(`Keyboard might be already hidden: ${error}`);
    }
  }

  async switchContext(context: string): Promise<void> {
    await driver.switchContext(context);
  }

  async installApp(appPath: string): Promise<void> {
    await browser.installApp(appPath);
  }

  async removeApp(bundleId: string): Promise<void> {
    await driver.removeApp(bundleId);
  }

  async getDeviceTime(): Promise<string> {
    return await driver.getDeviceTime();
  }

  async isAppInstalled(bundleId: string): Promise<boolean> {
    return await driver.isAppInstalled(bundleId);
  }

  async setLocation(latitude: number, longitude: number): Promise<void> {
    try {
      logger.info(
        `Setting device location to coordinates: ${latitude}, ${longitude}`,
      );

      // Set location based on platform
      if (driver.isAndroid) {

        await driver.setGeoLocation({
          latitude,
          longitude,
          altitude: 0,
        });
      } else if (driver.isIOS) {
        await browser.execute('mobile: setSimulatedLocation', {
          latitude,
          longitude,
          altitude: 0,
        });
      } else {
        throw new Error('Unsupported platform');
      }

      logger.info(
        `Successfully set device location to: ${latitude}, ${longitude}`,
      );
    } catch (error) {
      logger.error(`Failed to set device location: ${error}`);
      throw error;
    }
  }

  /**
   * Waits for a native element to be interactive
   * @param selector - Element selector
   * @param options - Wait options
   * @returns Promise<boolean>
   */
  async waitForNativeElement(
    selector: string,
    options?: IGestureOptions,
  ): Promise<boolean> {
    try {
      const element = await $(selector);
      const {
        timeout = 10000,
        interval = 500,
        timeoutMsg = `Element ${selector} not interactable`,
      } = options || {};

      await browser.waitUntil(
        async () => {
          try {
            const elementState: IElementWaitState = {
              isDisplayed: await element.isDisplayed(),
              isEnabled: await element.isEnabled(),
            };

            if (driver.isIOS) {
              elementState.isAccessible =
                (await element.getAttribute('accessible')) === 'true';
              return (
                elementState.isDisplayed &&
                elementState.isEnabled &&
                elementState.isAccessible
              );
            }

            if (driver.isAndroid) {
              elementState.isClickable =
                (await element.getAttribute('clickable')) === 'true';
              return (
                elementState.isDisplayed &&
                elementState.isEnabled &&
                elementState.isClickable
              );
            }

            return elementState.isDisplayed && elementState.isEnabled;
          } catch (error) {
            logger.debug(`Wait iteration failed: ${error}`);
            return false;
          }
        },
        {
          timeout,
          interval,
          timeoutMsg,
        },
      );
      logger.info(`Element ${selector} is now interactable`);
      return true;
    } catch (error) {
      logger.error(`waitForNativeElement failed for ${selector}: ${error}`);
      return false;
    }
  }

  /**
   * Waits for a specific state of a native element
   * @param selector - Element selector
   * @param state - Desired element state
   * @param options - Wait options
   * @returns Promise<boolean>
   */
  async waitForNativeElementState(
    selector: string,
    state: 'clickable' | 'visible' | 'enabled',
    options?: IGestureOptions,
  ): Promise<boolean> {
    try {
      const element = await $(selector);
      const {
        timeout = 10000,
        interval = 500,
        timeoutMsg = `Element ${selector} not in desired state: ${state}`,
      } = options || {};

      await browser.waitUntil(
        async () => {
          try {
            switch (state) {
              case 'visible':
                return await element.isDisplayed();
              case 'enabled':
                return await element.isEnabled();
              case 'clickable':
                if (driver.isAndroid) {
                  return (
                    (await element.getAttribute('clickable')) === 'true' &&
                    (await element.isDisplayed())
                  );
                }
                if (driver.isIOS) {
                  return (
                    (await element.getAttribute('accessible')) === 'true' &&
                    (await element.isDisplayed())
                  );
                }
                return (
                  (await element.isDisplayed()) && (await element.isEnabled())
                );
            }
          } catch (error) {
            logger.debug(`Wait state check failed: ${error}`);
            return false;
          }
        },
        {
          timeout,
          interval,
          timeoutMsg,
        },
      );
      logger.info(`Element ${selector} is now in desired state: ${state}`);
      return true;
    } catch (error) {
      logger.error(
        `waitForNativeElementState failed for ${selector}: ${error}`,
      );
      return false;
    }
  }

  // Backup method using multiple selector strategies
  private async clickWithFallback(options: {
    uiAutomator: string,
    resourceId?: string,
    accessibilityId?: string
  }) {
    const selectors = [];

    if (options.uiAutomator) {
      selectors.push(`android=${options.uiAutomator}`);
    }
    if (options.resourceId) {
      selectors.push(`~${options.accessibilityId}`);
    }
    if (options.accessibilityId) {
      selectors.push(`~${options.accessibilityId}`);
    }

    const element = await this.findElementWithRetry(selectors);
    await element.click();
  }

  private async findElementWithRetry(selectors: string[]) {
    for (const selector of selectors) {
      try {
        const element = await $(selector);
        if (await element.isDisplayed()) {
          return element;
        }
      } catch {
        continue;
      }
    }
    throw new Error('Element not found with any of the provided selectors');
  }

  /**
      * Checks if an element is visible on the screen using iOS-specific attributes
      * @param selector - The selector for the element (accessibility id, xpath, etc)
      * @returns Promise<boolean> - Whether the element is visible
      */
  async isElementVisible(selector: string): Promise<boolean> {
    try {
      const element = await $(selector);

      // Get element attributes using iOS-specific command
      const attributes = await element.getAttribute('visible');
      const isPresent = await element.isExisting();

      // Check if element is present and visible
      return isPresent && attributes !== null && attributes === 'true';
    } catch (error) {
      logger.error(`Error checking element visibility: ${error}`);
      return false;
    }
  }

  /**
  * Enhanced visibility check that also verifies element is within viewport
  * @param selector - The selector for the element
  * @returns Promise<boolean> - Whether the element is fully visible
  */
  async isElementFullyVisible(selector: string): Promise<boolean> {
    try {
      const element = await $(selector);

      // Check if element exists
      if (!(await element.isExisting())) {
        return false;
      }

      // Get element location and size
      const location = await element.getLocation();
      const size = await element.getSize();

      // Get window size
      const windowSize = await driver.getWindowSize();

      // Check if element is within viewport bounds
      const isWithinBounds =
        location.x >= 0 &&
        location.y >= 0 &&
        location.x + size.width <= windowSize.width &&
        location.y + size.height <= windowSize.height;

      // Get visibility attribute
      const isVisible = await element.getAttribute('visible') === 'true';

      return isWithinBounds && isVisible;
    } catch (error) {
      logger.error(`Error checking full visibility:${error}`);
      return false;
    }
  }

  /**
  * Waits for an element to become visible with timeout
  * @param selector - The selector for the element
  * @param timeout - Maximum time to wait in milliseconds
  * @returns Promise<boolean> - Whether the element became visible
  */
  async waitForElementVisible(selector: string, timeout = 10000): Promise<boolean> {
    try {
      const element = await $(selector);

      // Wait for element to exist first
      await element.waitForExist({ timeout });

      // Then check visibility repeatedly
      return await driver.waitUntil(
        async () => {
          const visible = await element.getAttribute('visible');
          return visible === 'true';
        },
        {
          timeout,
          timeoutMsg: `Element ${selector} did not become visible within ${timeout}ms`,
          interval: 500,
        },
      );
    } catch (error: unknown) {
      logger.error(`Error waiting for element visibility:${error}`);
      return false;
    }
  }

  /**
    * Enables Airplane Mode on Android devices or simulates it by setting
    * network conditions to 'offline' on iOS devices.
    *
    * Note: Direct control of iOS Airplane Mode via automation is restricted.
    * This function uses network throttling ('offline') to simulate the effect on iOS.
    * This function will only execute on mobile platforms (Android/iOS).
    *
    * @throws {Error} If the underlying command fails on either platform.
    */
  async enableAirplaneMode(): Promise<void> {
    // Ensure this only runs on mobile platforms
    if (!browser.isMobile) {
      logger.info(
        'enableAirplaneMode called on a non-mobile platform. No action taken.',
      );
      return; // Exit if not on a mobile device
    }

    // Determine the specific mobile platform
    const platform = driver.isAndroid
      ? 'Android'
      : driver.isIOS
        ? 'iOS'
        : 'Unknown Mobile';

    logger.info(
      `Attempting to enable Airplane Mode / set network offline on ${platform}...`,
    );

    try {
      if (platform === 'Android') {
        logger.info(
          'Enabling Airplane Mode on Android using adb shell commands...',
        );
        // Command to turn airplane mode setting ON
        await browser.execute('mobile: shell', {
          command: 'settings put global airplane_mode_on 1',
        });
        await browser.execute('mobile: shell', {
          command:
            'am broadcast -a android.intent.action.AIRPLANE_MODE --ez state true',
        });
        logger.info('Android Airplane Mode enabled via shell.');
      } else if (platform === 'iOS') {
        logger.info(
          'Simulating Airplane Mode on iOS by setting network to offline...',
        );
        await browser.throttleNetwork('offline');
        logger.info('iOS network throttled to offline.');
      } else {
        logger.info(
          `Platform "${platform}" not explicitly handled for enabling airplane mode. No action taken.`,
        );
      }
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error(
        `Failed to enable Airplane Mode or set network offline on ${platform}: ${message}`,
      );
      throw error;
    }
  }

  /**
   * Disables Airplane Mode on Android devices or resets network conditions
   * on iOS devices to simulate Airplane Mode being off.
   * (Refined version from previous request included for context)
   *
   * @throws {Error} If the underlying command fails on any platform.
   */
  async disableAirplaneMode(): Promise<void> {
    if (!browser.isMobile) {
      logger.info(
        'disableAirplaneMode called on a non-mobile platform. Assuming network reset.',
      );
      try {
        await browser.deleteNetworkConditions();
        logger.info('Desktop network conditions reset.');
      } catch (error: unknown) {
        const message = error instanceof Error ? error.message : String(error);
        logger.error(
          `Failed to reset network conditions on Desktop: ${message}`,
        );
        throw error;
      }
      return;
    }

    const platform = driver.isAndroid
      ? 'Android'
      : driver.isIOS
        ? 'iOS'
        : 'Unknown Mobile';

    logger.info(
      `Attempting to disable Airplane Mode / reset network on ${platform}...`,
    );

    try {
      if (platform === 'Android') {
        logger.info(
          'Disabling Airplane Mode on Android using adb shell commands...',
        );
        await browser.execute('mobile: shell', {
          command: 'settings put global airplane_mode_on 0',
        });
        await browser.execute('mobile: shell', {
          command:
            'am broadcast -a android.intent.action.AIRPLANE_MODE --ez state false',
        });
        logger.info('Android Airplane Mode disabled via shell.');
      } else if (platform === 'iOS') {
        logger.info('Resetting network conditions on iOS...');
        await browser.deleteNetworkConditions();
        logger.info('iOS network conditions reset.');
      } else {
        logger.info(
          `Platform "${platform}" not explicitly handled for disabling airplane mode. No action taken.`,
        );
      }
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error(
        `Failed to disable Airplane Mode or reset network conditions on ${platform}: ${message}`,
      );
      throw error;
    }
  }

}

export default new MobileActions();
