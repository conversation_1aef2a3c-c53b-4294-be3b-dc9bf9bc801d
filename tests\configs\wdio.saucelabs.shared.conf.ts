import type { Options } from '@wdio/types';
import { config as sharedConfig } from './wdio.shared.conf.ts';
import { getSauceCredentials } from '../support/helpers/index.ts';
import { ElementHighlighter } from '../support/helpers/ElementHighlighter.ts';

//
// Get the Sauce Labs credentials
const { sauceUsername, sauceAccessKey } = await getSauceCredentials();
// Define the highlight configuration
const highlightConfig = {
  enabled: process.env.HIGHLIGHT_ELEMENTS === 'true' || false,
  highlightDuration: 500,
  borderColor: 'red',
  borderWidth: '3px',
  backgroundColor: 'rgba(255, 0, 0, 0.2)',
};
// Create and export the highlighter instance
export const highlighter = new ElementHighlighter(highlightConfig);

// Create a global namespace for our custom Element Highlighter
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace WebdriverIO {
    interface Browser {
      highlightElements: boolean;
    }
  }
}
export const config: Options.Testrunner = {
  ...sharedConfig,
  //
  // =================
  // Service Providers
  // =================
  user: sauceUsername,
  key: sauceAccessKey,
  region: (process.env.SAUCE_REGION || 'us-west-1') as Options.SauceRegions,

  // Sauce Labs specific configuration
  hostname: process.env.SAUCE_REGION === 'us-west-1'
    ? 'ondemand.eu-central-1.saucelabs.com'
    : 'ondemand.us-west-1.saucelabs.com',
  protocol: 'https',
  path: '/wd/hub',
  port: 443,
  //
  // ============
  // Capabilities
  // ============
  // Are not configured here, they can be found in:
  // - wdio.saucelabs.desktop.conf.ts
  // - wdio.saucelabs.mobile.conf.ts
  //
  // ========
  // Services
  // ========
  // Reporting Configuration
  reporters: [
    'spec',
    ['allure', {
      outputDir: './reports/allure-results',
      disableWebdriverStepsReporting: false,
      disableWebdriverScreenshotsReporting: false,
      useCucumberStepReporter: true,
      addConsoleLogs: true,
      reportedEnvironments: ['deviceName', 'platformName', 'platformVersion', 'browserName', 'browserVersion'],
      // Cucumber-Specific Enhancements:
      suiteName: true, // Includes the Feature name as Suite
      storyName: true, // Includes the Scenario name as Story
      labels: [
        { name: 'feature', pattern: '.*\\.feature$' },  // Extracts Feature name
        { name: 'severity', value: 'normal' }, // Example: Default severity
        { name: 'tag', pattern: '@(.*)' }, // Extracts tags from scenarios
      ],
      // Add parameters from wdio config
      // parameter: {
      //   'browser': 'chrome',
      //   'version': '100.0'
      // }
    }],
    ['ocr', {
            // OCR service options
        }],
  ],
  services: (sharedConfig.services || []).concat([
    //
    // This service is needed for WDIO to make sure it can connect to Sauce Labs to:
    // - automatically update the names
    // - automatically update the status (passed/failed)
    // - automatically send the stacktrace in case of a failure
    //
    'sauce', 'appium',
    ['image-comparison', {
      baselineFolder: './tests/baselineImages',
      formatImageName: '{tag}-{logName}-{width}x{height}',
      screenshotPath: './reports/screenshots',
      savePerInstance: true,
      autoSaveBaseline: true,
      blockOutStatusBar: true,
      blockOutToolBar: true,
    }],
    //
    // This service is needed for the Sauce Visual service to work
    //
    [
      '@saucelabs/wdio-sauce-visual-service',
      // The options for the Sauce Visual service
      {
        buildName: process.env.BUILD_NAME || `Build-${new Date().toISOString()}`,
        branch: process.env.SAUCE_BRANCH || 'main',
        project: process.env.SAUCE_PROJECT || 'WDIO Cucumber Demo Project for' + process.env.SAUCE_USERNAME,
        captureDom: true,
        updateBaseline: false,
      },
    ],
  ]),
  // Hook to set up the highlighter and add the custom command
  before: function (_capabilities, _specs) {
    browser.highlightElements = highlightConfig.enabled;
    // Attach the highlighter to the browser object for easy access
    browser.addCommand('highlight', async function (this: WebdriverIO.Element) {
      return highlighter.highlight(this);
    }, true); // 'true' makes it an element command
  },
  

  // Clean up cookies and local storage between tests
  // afterScenario: async function () {
  //   //browser.reloadSession();
  //   try {
  //     await browser.deleteAllCookies();
  //     await browser.execute(() => {
  //       localStorage.clear();
  //       sessionStorage.clear();
  //     });
  //     console.log('Cleared browser state');
  //   } catch (error) {
  //     console.error('Failed to clear browser state', error);
  //   }
  // }
};
