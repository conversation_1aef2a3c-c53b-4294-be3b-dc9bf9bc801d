import { $ } from '@wdio/globals';
import Page from '../pages/Page.ts';

class CouponRedemptionPageObjects extends Page {

    get brandLogo() { return $('img[alt="Navbar Logo"]'); }
    get allCoupons() { return $$('div[data-testid="couponsListDescription"]'); }
    get allCpns() { return $('//div[@class="_coupons-list-container_av0im_1"]'); }
    get couponsPrice() { return $('//p[@class="_coupons-list-price_1yo3t_33"]'); }
    get couponsMessage() { return $('//p[@class="_coupons-list-description-text_1yo3t_1"]'); }
    get couponDescriptionStyle() { return $('//p[@class="_coupons-list-description-style_1yo3t_1"]'); }
    get couponsTnkuMesge() { return $('//div[@class="_coupons-list-header-welcome-text_1yo3t_10 _coupon-header-thankyou_1yo3t_264"]'); }
    get couponsExpDt() { return $('//div[@class="_coupons-list-header-expire-text_1yo3t_38"]'); }
    get welcmFstNameTxt() { return $('//*[@id="welcomeContainer"]'); }
    get hmbrgrMenu() { return $('//*[@alt="Ham Burger Menu"]'); }
    get btnSortOffers() { return $('//img[@alt="Sort By Value"] | //img[@alt="Sort By Expiration"]'); }
    get lblSortBy() { return $('//p[contains(text(),"Sort By:")]'); }
    get btn_ChooseStore() { return $('//div[text()="CHOOSE A STORE"]'); }
    get personalizedCoupon() { return $('(//div[@data-testid="couponsListDescription"])[2] | (//div[@data-testid="couponsListDescription"])[1] | (//div[contains(@id,"coupon-list-choice-container")])[1]'); }
    get lnk_Store() { return $('(//img[@alt="Right Arrow"])[1]'); }
    get lnk_Store2() { return $('(//img[@alt="Right Arrow"])[3]'); }
    get btn_RedeemNowAEM() { return $('//div[@data-testid="redeemNowButton"]'); }
    get dneleave_yes() { return $('//button[@id="iAmDoneBtn"]'); }
    get iAmDone() { return $('//p[text()="I’M DONE"]'); }
    get modalmessage() {return $('#modalMessage');}
    get modalmsgclosebutton() {return $('[data-testid="modalButtonTitle"]');}
    get letusKnow() { return $('//button[text()="Let Us Know"]'); }
    get noRtrntoCopn() { return $('//button[text()="No, Return to Coupon"]'); }
    get dneleave_yes_Grizzly() { return $('//div[text()="YES"]'); }
    get btn_NotNowAEM() { return $('//div[@data-testid="redeemLaterButton"]'); }
    get grizzly_Contentile1() { return $('//img[@alt="Content Tile Logo 1"] | //img[@id="firstTileId"]'); }
    get grizzly_Contentile2() { return $('//img[@alt="Content Tile Logo 2"] | //img[@id="secondTileId"]'); }
    get grizzly_Contentile1img() { return $('//img[@id="firstTileId"]'); }
    get grizzly_Contentile2img() { return $('//img[@id="secondTileId"]'); }
    get favouriteStoreCheckbox() { return $('//img[@alt="UnCheck Image"]'); }
    get unCheckFavrtStore() { return $('//img[@alt="Check Image"]'); }
    get storeListDownArrow() { return $('//img[@alt="Down Arrow"]'); }
    get goldStar() { return $('//img[@alt="gold star"]'); }
    get greyStar() { return $('//img[@alt="grey star"]'); }


}

export default new CouponRedemptionPageObjects();