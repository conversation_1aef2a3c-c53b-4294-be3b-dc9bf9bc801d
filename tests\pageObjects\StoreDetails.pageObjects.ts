
class StoreDetailsPageObject {
    
     
     get btnLoadMore() { return $('//div[@data-testid="loadMoreBtn"]'); }
     get txtZipCodeSearch() { return $('//input[@placeholder="Search by ZIP"]'); }
     get backBtn() { return $('//div[@class="_navbar-back-holder_4fx8k_102"]');}
     get errormsg_PopupStoreList() { return $('//div[@data-testid="modalContent"]');}
     get errmsgCloseBtn() { return $('//div[@data-testid="modalButtonTitle"]');}
     get lnk_StoreListMapView() { return $('//img[@alt="inactive map icon"]');}
    
    get lnkSelectStrMapViw() { return $('//gmp-advanced-marker[@role="button"][1]');}
    get lnkRightArrowMpViw() { return $('//*[@data-testid="right-arrow-icon"]');}
    
    get btnshowdirections() {return $("button[data-testid='show-dir-btn']");}

    get redeemNowBtn() { return $('//p[text()="REDEEM NOW"]');}
    get mapItRedeempg() { return $('//div[@data-testid="mapItElement"]');}
    get downArrowRdmPg() { return $('//img[@alt="Down Arrow"]');}
    get favrtStoreTxt() { return $('//div[text()="Make this store your favorite."]');}
    get closeBtnMapVw() { return $('//button[@title="Close"]');}
    get mapItBtnMapVwPg() { return $('//b[text()="map it"]');}
   get showDirection() { return $('//button[text()="Show Directions"]');} 
   
    get errormsg_PopupStoreCoupon() { return $('//div[@data-testid="modalContent"]');}
    get errormsg_CloseBtnStoreCoupon() { return $('//div[@data-testid="modalButtonTitle"]');}
    get carIcon() { return $('//img[@class="_car-image_1d3af_28"]');}
    get distanceTxt() { return $('//span[@class="_distance_1d3af_28"]');}
    get storeName() { return $('//b[@class="_store-name-text_1d3af_28"]');}
    get storeAddress() { return $('(//div[@class="_address_1d3af_28"])[1]');} 

    }

export default new StoreDetailsPageObject();
