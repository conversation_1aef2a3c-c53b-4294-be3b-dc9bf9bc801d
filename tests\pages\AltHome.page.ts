import Page from './Page.ts';
import { Key } from 'webdriverio';
import elementActions from '../support/actions/element.actions.ts';
import couponHomePageObject from '../pageObjects/CouponHome.pageObject.ts';
import LocationServicesPageObjects from '../pageObjects/LocationServices.pageObjects.ts';
import AltHomePageObjects from '../pageObjects/AltHome.pageObjects.ts';
import ocrClickUtil from '../support/utils/ocrClickUtil.ts';
import sessionManager from '../support/utils/sessionManager.ts';

class AltHome extends Page {

    public async validateUserOnActiveCouponPage() {
        couponHomePageObject.allCoupons.forEach(async coupon => {
            await expect(await coupon).toBeDisplayed();
        });
    }

    /**
     * Clicks tile1 and handles popup immediately to prevent hanging
     * This method runs popup handling in parallel with the click to avoid timeouts
     */
    private async clickTile1WithPopupHandling(): Promise<void> {
        try {
            console.log('Starting tile1 click with popup handling...');

            // Start popup handling in the background immediately
           // this.handlePopupInBackground();

            // Perform the click
            await this.performTile1Click();

            // Wait a moment for popup handling to complete
            await driver.pause(2000);

            console.log('Tile1 click and popup handling completed');

        } catch (error) {
            console.log('Error in tile1 click with popup handling:', error);
            throw error;
        }
    }

    /**
     * Performs the actual click on tile1 using JavaScript click with timeout
     */
    private async performTile1Click(): Promise<void> {
        try {
            console.log('Performing tile1 click using JavaScript...');

            // Use JavaScript click with a timeout to prevent hanging
            const tile1Element = await AltHomePageObjects.tile1Image;

            // Use Promise.race to timeout the JavaScript click if it hangs
            await Promise.race([
                driver.execute('arguments[0].click();', tile1Element),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('JavaScript click timeout')), 5000),
                ),
            ]);

            console.log('Tile1 JavaScript click completed');
        } catch (error) {
            console.log('JavaScript click failed or timed out:', error);
        }
    }

    /**
     * Handles popup in background immediately after click starts
     */
    private async handlePopupInBackground(): Promise<void> {
        try {
            console.log('Starting background popup handling...');

            // Wait a short moment for popup to appear
            await driver.pause(1000);

            // First try to handle Safari popup permission dialog (for iOS)
            if (driver.isIOS) {
                // Add timeout to prevent hanging on getContext
                await Promise.race([
                    this.handleSafariPopupPermission(),
                    new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('Safari popup handling timeout')), 10000),
                    ),
                ]);
            }

            // Then try to handle JavaScript alerts with timeout
            await Promise.race([
                this.handleJavaScriptAlert(),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('JavaScript alert handling timeout')), 5000),
                ),
            ]);

        } catch (error) {
            console.log('Error in background popup handling:', error);
        }
    }

    /**
     * Handles Safari popup permission dialog that appears when clicking tile1
     */
    private async handleSafariPopupPermission(): Promise<void> {
        try {
            console.log('Checking for Safari popup permission dialog...');

            // Get current context to switch back later with timeout
            const currentContext = await Promise.race([
                driver.getContext(),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('getContext timeout')), 3000),
                ),
            ]) as string;

            // Switch to native context to handle Safari popup with timeout
            await Promise.race([
                driver.switchContext('NATIVE_APP'),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('switchContext timeout')), 3000),
                ),
            ]);

            // Look for "Allow" button in Safari popup permission dialog
            const allowButton = $('//XCUIElementTypeButton[@name="Allow"]');

            // Wait for the Allow button to appear
            await allowButton.waitForDisplayed({
                timeout: 10000,
                timeoutMsg: 'Safari Allow button not found',
            });

            if (await allowButton.isDisplayed()) {
                await allowButton.click();
                console.log('Safari popup Allow button clicked successfully');
                await driver.pause(1000);
            }

            // Switch back to webview context
            const contexts = await driver.getContexts();
            const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
            if (webviewContext) {
                await driver.switchContext(webviewContext.toString());
                console.log('Switched back to webview context');
            } else if (currentContext) {
                await driver.switchContext(currentContext);
                console.log('Switched back to original context');
            }

        } catch (error) {
            console.log('Safari popup permission dialog not found or already handled:', error);

            // Ensure we're back in webview context even if there was an error
            try {
                const contexts = await driver.getContexts();
                const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));
                if (webviewContext) {
                    await driver.switchContext(webviewContext.toString());
                }
            } catch (contextError) {
                console.log('Error switching back to webview context:', contextError);
            }
        }
    }

    /**
     * Handles JavaScript alerts
     */
    private async handleJavaScriptAlert(): Promise<void> {
        try {
            console.log('Checking for JavaScript alerts...');

            // Wait for alert to appear with a reasonable timeout
            await driver.waitUntil(
                async () => {
                    try {
                        await driver.getAlertText();
                        return true;
                    } catch (error) {
                        return false;
                    }
                },
                {
                    timeout: 10000,
                    interval: 500,
                    timeoutMsg: 'JavaScript alert did not appear within 10 seconds',
                },
            );

            // Get alert text for logging
            const alertText = await driver.getAlertText();
            console.log('JavaScript alert text:', alertText);

            // Accept the alert
            await driver.acceptAlert();
            console.log('JavaScript alert accepted successfully');

        } catch (error) {
            console.log('No JavaScript alert found:', error);
        }
    }

    async validateUserIsOnAltHomePage() {
        await expect(await AltHomePageObjects.redeemNowBtn).toBeDisplayed();
        await expect(await AltHomePageObjects.navBarLogo).toBeDisplayed();
        await expect(await AltHomePageObjects.couponCount).toBeDisplayed();
        await expect(await AltHomePageObjects.welcomeBackMsg).toBeDisplayed();
    }

    async validateAltHomePage(brand: string): Promise<void> {
        try {
            console.log(`Starting Alt Home Page validation for brand: ${brand}`);

            // Initialize session monitoring
            sessionManager.markSessionStart();
            sessionManager.logSessionStats();

            if (driver.isIOS) {
                // Validate basic page elements first
                await sessionManager.safeExecute(
                    () => this.validateBasicPageElements(brand),
                    'validate basic page elements',
                    15000,
                );

                // Check session health before proceeding
                const isHealthy = await sessionManager.performHealthCheck();
                if (!isHealthy) {
                    console.warn('Session health check failed, attempting recovery...');
                    const recovered = await sessionManager.attemptSessionRecovery();
                    if (!recovered) {
                        throw new Error('Session recovery failed');
                    }
                }

                // Validate tile images
                await sessionManager.safeExecute(
                    () => this.validateTileImages(),
                    'validate tile images',
                    10000,
                );

                // Handle tile1 click and popups with improved error handling
                await sessionManager.safeExecute(
                    () => this.handleTile1ClickWithTimeouts(),
                    'handle tile1 click and popups',
                    45000,
                );

                sessionManager.logSessionStats();
                console.log('Alt Home Page validation completed successfully');
            }
        } catch (error) {
            console.error('Error in validateAltHomePage:', error);
            sessionManager.logSessionStats();
            throw error;
        }
    }

    /**
     * Validates basic page elements based on brand
     */
    private async validateBasicPageElements(brand: string): Promise<void> {
        console.log('Validating basic page elements...');

        await expect(AltHomePageObjects.redeemNowBtn).toBeDisplayed();
        await expect(AltHomePageObjects.navBarLogo).toBeDisplayed();
        await expect(AltHomePageObjects.couponCount).toBeDisplayed();
        await expect(AltHomePageObjects.welcomeBackMsg).toBeDisplayed();

        if (brand == 'GRIZZLYMO') {
            await expect(AltHomePageObjects.warningText).toBeDisplayed();
            await expect(AltHomePageObjects.underageSaleText).toBeDisplayed();
            await expect(AltHomePageObjects.nicotineProductsText).toBeDisplayed();
        } else if (brand == 'CAMEL') {
            await expect(AltHomePageObjects.sgwcamel).toBeDisplayed();
        }

        console.log('Basic page elements validated successfully');
    }

    /**
     * Validates tile images are displayed
     */
    private async validateTileImages(): Promise<void> {
        console.log('Validating tile images...');

        await expect(AltHomePageObjects.tile1Image).toBeDisplayed();
        await expect(AltHomePageObjects.tile2Image).toBeDisplayed();
        await expect(AltHomePageObjects.tile3Image).toBeDisplayed();

        console.log('Tile images validated successfully');
    }

    /**
     * Handles tile1 click and popup management with proper timeouts and error handling
     */
    private async handleTile1ClickWithTimeouts(): Promise<void> {
        try {
            console.log('Starting tile1 click with improved timeout handling...');

            // Perform tile1 click with timeout
            await this.performTile1ClickWithTimeout();

            // Handle popups with timeout and fallback
            await this.handlePopupsWithTimeout();

            console.log('Tile1 click and popup handling completed successfully');

        } catch (error) {
            console.log('Error in tile1 click handling, attempting recovery:', error);
            await this.attemptRecovery();
        }
    }

    /**
     * Performs tile1 click with proper timeout handling
     */
    private async performTile1ClickWithTimeout(): Promise<void> {
        try {
            console.log('Performing tile1 click with timeout...');

            const tile1Element = AltHomePageObjects.tile1Image;

            // Use Promise.race with shorter timeout to prevent hanging
            await Promise.race([
                driver.execute('arguments[0].click();', tile1Element),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Tile1 click timeout after 3 seconds')), 3000),
                ),
            ]);

            console.log('Tile1 click completed successfully');

            // Short pause to allow popup to appear
            await driver.pause(1000);

        } catch (error) {
            console.log('Tile1 click failed or timed out:', error);
            throw error;
        }
    }

    /**
     * Handles popups with timeout and proper error handling
     */
    private async handlePopupsWithTimeout(): Promise<void> {
        try {
            console.log('Handling popups with timeout...');

            // Try to handle Safari permission popup with timeout
            await this.handleSafariPermissionWithTimeout();

            // Handle mobile notification permission with timeout
            await this.handleMobileNotificationWithTimeout();

            // Handle location services with timeout
            await this.handleLocationServicesWithTimeout();

        } catch (error) {
            console.log('Popup handling completed with some errors (this may be normal):', error);
            // Don't throw here as popups may not always appear
        }
    }

    /**
     * Handles Safari permission popup with timeout
     */
    private async handleSafariPermissionWithTimeout(): Promise<void> {
        try {
            console.log('Checking for Safari permission popup...');

            // Switch to native context with timeout
            await Promise.race([
                driver.switchContext('NATIVE_APP'),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Switch to native context timeout')), 5000),
                ),
            ]);

            // Try OCR click with shorter timeout
            const allowClicked = await Promise.race([
                ocrClickUtil.clickOnText('Allow', 2, 500),
                new Promise<boolean>((resolve) =>
                    setTimeout(() => resolve(false), 3000),
                ),
            ]);

            if (allowClicked) {
                console.log('Successfully clicked Allow button via OCR');
            } else {
                console.log('Allow button not found or clicked via OCR, trying alternative methods');
                await this.tryAlternativeAllowClick();
            }

        } catch (error) {
            console.log('Safari permission handling failed:', error);
            // Try alternative approach
            await this.tryAlternativeAllowClick();
        }
    }

    /**
     * Alternative method to click Allow button
     */
    private async tryAlternativeAllowClick(): Promise<void> {
        try {
            console.log('Trying alternative Allow button click methods...');

            // Try direct element click with accessibility id
            const allowButton = await driver.$('~Allow');
            if (await allowButton.isExisting()) {
                await allowButton.click();
                console.log('Clicked Allow button via accessibility id');
                return;
            }

            // Try with different selectors
            const allowOnceButton = await driver.$('~Allow Once');
            if (await allowOnceButton.isExisting()) {
                await allowOnceButton.click();
                console.log('Clicked Allow Once button');
                return;
            }

            console.log('No Allow buttons found via alternative methods');

        } catch (error) {
            console.log('Alternative Allow click methods failed:', error);
        }
    }

    /**
     * Handles mobile notification permission with timeout
     */
    private async handleMobileNotificationWithTimeout(): Promise<void> {
        try {
            console.log('Handling mobile notification permission...');

            // Try to handle notification permission popup
            await Promise.race([
                this.handleMobileNotificationPermission(),
                new Promise((resolve) => setTimeout(resolve, 5000)),
            ]);

            console.log('Mobile notification permission handled');

        } catch (error) {
            console.log('Mobile notification permission handling failed:', error);
        }
    }

    /**
     * Handles location services with timeout
     */
    private async handleLocationServicesWithTimeout(): Promise<void> {
        try {
            console.log('Handling location services...');

            if (driver.isIOS) {
                // Try to click location services allow button if it exists
                await Promise.race([
                    elementActions.clickElementIfExistsAndVisible(LocationServicesPageObjects.iOSLocationServiceAllowBtn),
                    new Promise((resolve) => setTimeout(resolve, 3000)),
                ]);

                // Switch back to webview context
                await this.switchToWebviewContext();
            }

            console.log('Location services handled');

        } catch (error) {
            console.log('Location services handling failed:', error);
        }
    }

    /**
     * Switches back to webview context safely
     */
    private async switchToWebviewContext(): Promise<void> {
        try {
            console.log('Switching back to webview context...');

            const contexts = await Promise.race([
                driver.getContexts(),
                new Promise<string[]>((_, reject) =>
                    setTimeout(() => reject(new Error('getContexts timeout')), 3000),
                ),
            ]);

            const webviewContext = contexts.find(context => context.toString().includes('WEBVIEW'));

            if (webviewContext) {
                await Promise.race([
                    driver.switchContext(webviewContext.toString()),
                    new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('switchContext timeout')), 3000),
                    ),
                ]);
                console.log('Successfully switched back to webview context');
            } else {
                console.log('No webview context found');
            }

        } catch (error) {
            console.log('Failed to switch to webview context:', error);
        }
    }

    /**
     * Attempts recovery when tile click fails
     */
    private async attemptRecovery(): Promise<void> {
        try {
            console.log('Attempting recovery from tile click failure...');

            // Try to get back to a known state
            await driver.pause(2000);

            // Try to switch back to webview if we're in native context
            await this.switchToWebviewContext();

            // Check if we can still interact with the page
            const isPageAccessible = await this.checkPageAccessibility();

            if (isPageAccessible) {
                console.log('Page is still accessible after recovery');
            } else {
                console.log('Page may not be accessible after recovery');
            }

        } catch (error) {
            console.log('Recovery attempt failed:', error);
        }
    }

    /**
     * Checks if the page is still accessible
     */
    private async checkPageAccessibility(): Promise<boolean> {
        try {
            // Try to find a basic element to verify page accessibility
            const redeemBtn = AltHomePageObjects.redeemNowBtn;
            return await redeemBtn.isExisting();
        } catch (error) {
            console.log('Page accessibility check failed:', error);
            return false;
        }
    }

}



export default new AltHome();

