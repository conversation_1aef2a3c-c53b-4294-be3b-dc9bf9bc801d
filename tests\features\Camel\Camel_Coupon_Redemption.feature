Feature: Camel PWA - Coupon Redemption
    As a RJR user
    I want to access and validate the coupon Redeem page
    So that I can ensure the coupon functionality works correctly

    @CamelCouponRedemption
    Scenario Outline: Validate Camel PWA coupon Redemption
        Given I am on the <Brand> login page at <URL>
        And  I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I select a store from store list page
        When I click on the Redeem Now button
        Then I click on the I'm Done button

        @CamelCouponRedemption_QAstage
        Examples:
            | Brand | URL                          | Username                                     | Password  | Latitude  | Longitude | appName |
            | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.099861 | -80.2446  | Camel   |

@CamelCouponRedemptionatdifferentRetailers
    Scenario Outline: Validate that the user is able to successfully redeem coupons for the brand <Brand> at <Retailer> Store
        Given I am on the <Brand> login page at <URL>
        And  I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I select a store from store list page
        When I click on the Redeem Now button
        Then I click on the I'm Done button

        @CamelCouponRedemptionRetailers_QAstage
        Examples:
            | Brand | URL                          | Username                                     | Password  | Latitude          | Longitude           | appName |Retailer               |
            #| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 29.702034         | -98.093590          | Camel   |Murphy                 |
            | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 38.413445         |  -81.799462         | Camel   |Sheetz                 |
            #| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 34.93009603043261 | -83.73309065394571  | Camel   |MOM DAD BLUERIDGE LLC  |
            #| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 42.76001750572921 | -71.4535689468692   | Camel   |Speedway Store         |
            #| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 37.226959         | -80.011164             | Camel   |Smokin Store           |
            #| CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 37.6362073,       | -77.5496002           | Camel   |RVA Tobacco            |
    
    
    @CamelCouponRedemptionMapView
    Scenario Outline: Validate Camel coupon Redemption from Map View
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I navigate to store list map view page
        When I select a store from map view
        Then I validate the user is on Redeem now page
        When I click on the Redeem Now button
        Then I click on the I'm Done button

        @CamelCouponRedemptionfromMapView_QAstage
        Examples:
            | Brand | URL                          | Username                                          | Password  | Latitude  | Longitude | appName |
            | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.099861 | -80.2446  | Camel   |


    @CamelContentPage
    Scenario Outline: Validate Camel content tiles
        Given I am on the <Brand> login page at <URL>
        And   I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I select a store from store list page
        When I click on the Redeem Now button
        Then I click on the I'm Done button
        Then I Validate the <Brand> Content Page

        @ContentPage_QAstage
        Examples:
            | Brand | URL                          | Username                               | Password  | Latitude  | Longitude | appName |
            | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.099861 | -80.2446  | Camel   |


    @CamelFavouriteStore
    Scenario Outline: Validate Camel Favourite Store functionality
        Given I am on the <Brand> login page at <URL>
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I select a coupon and click on choose a store button
        Then I select a store from store list page
        When I mark the store as favorite
        Then I navigate to Store List and validate the favoritestore
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link

        @CamelFavouriteStore_QAstage
        Examples:
            | Brand | URL                          | Username                                | Password  | Latitude  | Longitude | appName |
            | CAMEL | https://mobile-qa.camel.com/ | <EMAIL> | Password1 | 36.099861 | -80.2446  | Camel   |

