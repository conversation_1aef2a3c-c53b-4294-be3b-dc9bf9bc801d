Feature: Camel PWA - Hamburger Menu
    As a RJR user
    I want to access and validate the Hamberger Menu
    So that I can ensure the Hmberger Menu Links works correctly

    @CamelHamburgerMenu 
    Scenario Outline: Validate Camel Coupons in HamburgerMenu
        Given I am on the <Brand> login page at "<URL>"
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
       Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I navigate to Hamburger Menu
        Then I validate the elements in Grizzly Hamburger Menu
        When I click on Grizzly coupons link
        Then I Validate the Coupon Home Page
        When I navigate to Hamburger Menu
        Then I validate the elements in Grizzly Hamburger Menu
         Then I click on <Brand> Logout link
        
@CamelCouponsHamburgerMenu_QAstage
Examples:
| Brand     | URL                         | Username                                     | Password  | Latitude          | Longitude          |appName                 |
| CAMEL     | https://mobile-qa.camel.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|

@CamelCouponsHamburgerMenu_Prod
Examples:
| Brand     | URL                                     | Username                                     | Password  | Latitude          | Longitude          |appName                 |
| CAMEL     | https://www.mobile.camel.com/ | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|


@CamelLogoutHamburgerMenu 
    Scenario Outline: Validate Camel Logout from HamburgerMenu
        Given I am on the <Brand> login page at "<URL>"
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        Then I Validate the Coupon Home Page
        When I navigate to Hamburger Menu
        Then I click on <Brand> Logout link
        
    @CamelLogoutHamburgerMenu_QAstage
    Examples:
    | Brand     | URL                         | Username                                     | Password  | Latitude          | Longitude          |appName                 |
    | CAMEL     | https://mobile-qa.camel.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|

    @CamelLogoutHamburgerMenu_Prod
    Examples:
    | Brand     | URL                                          | Username                                     | Password  | Latitude          | Longitude          |appName                 |
    | CAMEL     | https://www.mobile.camel.com/      | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel|

@CamelMobileSiteHamburgerMenu 
    Scenario Outline: Validate Camel Mobile Site from HamburgerMenu
        Given I am on the <Brand> login page at "<URL>"
        And I set my location to "<Latitude>" and "<Longitude>"
        When I log in with valid username "<Username>" and password "<Password>" for "<Brand>"
        Then I click the "Understood" button
        When I navigate to <Brand> offers page
        When I navigate and add <Brand> PWA link
        When I launch the PWA App <appName> and app activity as <appActivity>
        Then I login to PWA with valid user id <Username> and password <Password> for the brand <Brand>
        When I navigate to <Brand> offers page
        When I navigate to Hamburger Menu
        When I click on Grizzly MobileSite link
         Then I click on <Brand> Logout link
            
@CamelMobileSiteHamburgerMenu_QAstage
Examples:
| Brand     | URL                         | Username                                     | Password  | Latitude          | Longitude          |appName       |
| CAMEL     | https://mobile-qa.camel.com | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel         |

@CamelMobileSiteHamburgerMenu_Prod
Examples:
| Brand     | URL                                | Username                                     | Password  | Latitude          | Longitude          |appName     |
| CAMEL     | https://www.mobile.camel.com/      | <EMAIL> | Password1 | 36.09947034604555 | -80.24355602883584 |Camel       |